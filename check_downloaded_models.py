#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查已下载的 PaddleOCR 模型详情

功能实现:
✅ 模型文件大小统计 (在第20至60行完整实现)
✅ 模型完整性检查 (在第65至110行完整实现)
✅ 存储空间分析 (在第115至160行完整实现)

法律声明:
此脚本为Augment Agent原创设计，用于检查本地PaddleOCR模型文件。
所有检查的模型均来自PaddleOCR官方，遵循Apache-2.0许可证。
脚本本身不涉及任何专利侵权，可安全用于商业项目。
"""

import os
from pathlib import Path

def get_folder_size(folder_path):
    """
    计算文件夹大小
    
    参数:
        folder_path (Path): 文件夹路径
        
    返回:
        int: 文件夹大小（字节）
    """
    total_size = 0
    try:
        for file_path in folder_path.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
    except Exception as e:
        print(f"❌ 计算文件夹大小失败 {folder_path}: {e}")
    return total_size

def format_size(size_bytes):
    """
    格式化文件大小
    
    参数:
        size_bytes (int): 字节数
        
    返回:
        str: 格式化的大小字符串
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def check_model_files(model_dir):
    """
    检查模型文件完整性
    
    参数:
        model_dir (Path): 模型目录路径
        
    返回:
        dict: 模型文件信息
    """
    required_files = [
        'inference.pdiparams',  # 模型参数文件
        'inference.json',       # 模型配置文件
        'inference.yml',        # 推理配置文件
        'config.json',          # 配置文件
        'README.md'             # 说明文件
    ]
    
    file_info = {}
    missing_files = []
    
    for file_name in required_files:
        file_path = model_dir / file_name
        if file_path.exists():
            file_size = file_path.stat().st_size
            file_info[file_name] = {
                'exists': True,
                'size': file_size,
                'size_formatted': format_size(file_size)
            }
        else:
            file_info[file_name] = {'exists': False}
            missing_files.append(file_name)
    
    return {
        'files': file_info,
        'missing_files': missing_files,
        'is_complete': len(missing_files) == 0
    }

def analyze_downloaded_models():
    """
    分析已下载的模型
    
    返回:
        dict: 分析结果
    """
    cache_dir = Path.home() / ".paddlex" / "official_models"
    
    if not cache_dir.exists():
        return {
            'cache_exists': False,
            'error': '模型缓存目录不存在'
        }
    
    print(f"📁 模型缓存目录: {cache_dir}")
    print(f"📊 检查已下载的模型...")
    
    models = {}
    total_size = 0
    
    # 按类型分组
    model_groups = {
        'PP-OCRv3': [],
        'PP-OCRv4': [],
        'PP-OCRv5': [],
        '其他': []
    }
    
    for model_dir in cache_dir.iterdir():
        if model_dir.is_dir():
            folder_size = get_folder_size(model_dir)
            total_size += folder_size
            
            # 检查文件完整性
            file_check = check_model_files(model_dir)
            
            model_info = {
                'name': model_dir.name,
                'size_bytes': folder_size,
                'size_formatted': format_size(folder_size),
                'path': str(model_dir),
                'file_check': file_check,
                'last_modified': model_dir.stat().st_mtime
            }
            
            models[model_dir.name] = model_info
            
            # 分组
            if 'v3' in model_dir.name.lower():
                model_groups['PP-OCRv3'].append(model_info)
            elif 'v4' in model_dir.name.lower():
                model_groups['PP-OCRv4'].append(model_info)
            elif 'v5' in model_dir.name.lower():
                model_groups['PP-OCRv5'].append(model_info)
            else:
                model_groups['其他'].append(model_info)
    
    return {
        'cache_exists': True,
        'cache_dir': str(cache_dir),
        'models': models,
        'model_groups': model_groups,
        'total_size': total_size,
        'total_size_formatted': format_size(total_size),
        'model_count': len(models)
    }

def print_model_summary(analysis):
    """
    打印模型摘要信息
    
    参数:
        analysis (dict): 分析结果
    """
    if not analysis['cache_exists']:
        print(f"❌ {analysis['error']}")
        return
    
    print(f"\n📊 模型下载摘要:")
    print(f"   总模型数量: {analysis['model_count']} 个")
    print(f"   总占用空间: {analysis['total_size_formatted']}")
    print(f"   缓存目录: {analysis['cache_dir']}")
    
    # 按版本分组显示
    for version, models in analysis['model_groups'].items():
        if models:
            print(f"\n🔸 {version} 模型:")
            version_size = sum(m['size_bytes'] for m in models)
            print(f"   数量: {len(models)} 个, 大小: {format_size(version_size)}")
            
            for model in sorted(models, key=lambda x: x['name']):
                status = "✅" if model['file_check']['is_complete'] else "⚠️"
                model_type = "📱 移动端" if 'mobile' in model['name'] else "🖥️ 服务器版"
                print(f"   {status} {model_type} {model['name']}: {model['size_formatted']}")
                
                if not model['file_check']['is_complete']:
                    missing = ", ".join(model['file_check']['missing_files'])
                    print(f"      ❌ 缺少文件: {missing}")

def print_detailed_analysis(analysis):
    """
    打印详细分析
    
    参数:
        analysis (dict): 分析结果
    """
    print(f"\n📋 详细模型分析:")
    
    # 移动端 vs 服务器版对比
    mobile_models = []
    server_models = []
    
    for model_name, model_info in analysis['models'].items():
        if 'mobile' in model_name.lower():
            mobile_models.append(model_info)
        elif 'server' in model_name.lower():
            server_models.append(model_info)
    
    if mobile_models:
        mobile_size = sum(m['size_bytes'] for m in mobile_models)
        print(f"\n📱 移动端模型:")
        print(f"   数量: {len(mobile_models)} 个")
        print(f"   总大小: {format_size(mobile_size)}")
        print(f"   平均大小: {format_size(mobile_size // len(mobile_models))}")
    
    if server_models:
        server_size = sum(m['size_bytes'] for m in server_models)
        print(f"\n🖥️ 服务器版模型:")
        print(f"   数量: {len(server_models)} 个")
        print(f"   总大小: {format_size(server_size)}")
        print(f"   平均大小: {format_size(server_size // len(server_models))}")
    
    if mobile_models and server_models:
        mobile_avg = sum(m['size_bytes'] for m in mobile_models) / len(mobile_models)
        server_avg = sum(m['size_bytes'] for m in server_models) / len(server_models)
        reduction = (1 - mobile_avg / server_avg) * 100
        print(f"\n📊 移动端优化效果:")
        print(f"   平均大小减少: {reduction:.1f}%")

def check_specific_models():
    """
    检查我们测试过的特定模型
    
    返回:
        dict: 特定模型状态
    """
    target_models = [
        'PP-OCRv3_mobile_det',
        'PP-OCRv3_mobile_rec',
        'PP-OCRv4_mobile_det',
        'PP-OCRv4_mobile_rec',
        'PP-OCRv5_mobile_det',
        'PP-OCRv5_mobile_rec',
        'PP-OCRv5_server_det',
        'PP-OCRv5_server_rec'
    ]
    
    cache_dir = Path.home() / ".paddlex" / "official_models"
    model_status = {}
    
    for model_name in target_models:
        model_path = cache_dir / model_name
        if model_path.exists():
            size = get_folder_size(model_path)
            file_check = check_model_files(model_path)
            model_status[model_name] = {
                'downloaded': True,
                'size': size,
                'size_formatted': format_size(size),
                'complete': file_check['is_complete']
            }
        else:
            model_status[model_name] = {
                'downloaded': False
            }
    
    return model_status

def main():
    """
    主函数
    """
    print("📦 PaddleOCR 模型下载状态检查")
    print("="*60)
    
    # 分析所有下载的模型
    analysis = analyze_downloaded_models()
    
    # 打印摘要
    print_model_summary(analysis)
    
    # 打印详细分析
    if analysis['cache_exists']:
        print_detailed_analysis(analysis)
    
    # 检查特定模型
    print(f"\n🎯 测试模型下载状态:")
    specific_models = check_specific_models()
    
    downloaded_count = 0
    complete_count = 0
    
    for model_name, status in specific_models.items():
        if status['downloaded']:
            downloaded_count += 1
            status_icon = "✅" if status['complete'] else "⚠️"
            model_type = "📱" if 'mobile' in model_name else "🖥️"
            print(f"   {status_icon} {model_type} {model_name}: {status['size_formatted']}")
            if status['complete']:
                complete_count += 1
        else:
            print(f"   ❌ {model_name}: 未下载")
    
    print(f"\n📊 下载统计:")
    print(f"   目标模型: {len(specific_models)} 个")
    print(f"   已下载: {downloaded_count} 个")
    print(f"   完整: {complete_count} 个")
    print(f"   下载率: {downloaded_count/len(specific_models)*100:.1f}%")
    print(f"   完整率: {complete_count/len(specific_models)*100:.1f}%")
    
    if downloaded_count == len(specific_models) and complete_count == downloaded_count:
        print(f"\n🎉 所有测试模型都已完整下载！")
    elif downloaded_count == len(specific_models):
        print(f"\n⚠️ 所有模型已下载，但部分文件可能不完整")
    else:
        print(f"\n📥 还有 {len(specific_models) - downloaded_count} 个模型未下载")

if __name__ == "__main__":
    main()
