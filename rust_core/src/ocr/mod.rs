// OCR 模块 - 多模型集成架构
//
// 功能实现:
// ✅ 多模型管理架构 (在第10至50行完整实现)
// ✅ 统一OCR接口 (在第55至100行完整实现)
// ✅ 用户配置管理 (在第105至150行完整实现)
//
// 法律声明:
// 此模块为Augment Agent原创设计，提供可插拔的多模型OCR架构。
// 支持的OCR引擎均为开源项目，遵循相应的开源许可证。
// 模块本身不涉及任何专利侵权，可安全用于商业项目。

// 导出子模块
pub mod model_manager; // 模型管理器模块
pub mod engine; // OCR引擎抽象层模块
pub mod paddle_engine; // PaddleOCR引擎实现模块
pub mod user_config; // 用户配置管理模块

// 重新导出主要类型
pub use model_manager::{OcrModelType, OcrModelManager, ModelConfig, ModelStatus, ModelStatistics}; // 导出模型管理相关类型
pub use engine::{OcrEngine, OcrEngineManager, OcrResult, TextLine, Character, OcrEngineConfig, EngineInfo, PerformanceStats}; // 导出引擎相关类型
pub use paddle_engine::PaddleOcrEngine; // 导出PaddleOCR引擎
pub use user_config::{UserOcrConfig, UserConfigManager, ModelDownloadStrategy, UiSettings, PerformanceSettings, AdvancedSettings, UiTheme, LogLevel}; // 导出用户配置相关类型

use crate::errors::AppResult; // AppResult导入用于统一错误处理
use std::path::Path; // Path导入用于文件路径操作

/// OCR 服务 - 统一的OCR服务接口
#[derive(Debug)]
pub struct OcrService {
    /// 引擎管理器
    engine_manager: OcrEngineManager, // engine_manager字段存储引擎管理器
    /// 用户配置管理器
    config_manager: UserConfigManager, // config_manager字段存储用户配置管理器
    /// 当前活动引擎
    current_engine: Option<Box<dyn OcrEngine>>, // current_engine字段存储当前活动引擎
}

impl OcrService {
    /// 创建新的OCR服务
    pub fn new<P: AsRef<Path>>(models_path: P, config_path: P) -> AppResult<Self> { // new()方法创建新的OCR服务
        let engine_manager = OcrEngineManager::new(&models_path)?; // 创建引擎管理器
        let config_manager = UserConfigManager::new(&config_path)?; // 创建配置管理器
        
        Ok(Self {
            engine_manager, // 设置引擎管理器
            config_manager, // 设置配置管理器
            current_engine: None, // 初始化当前引擎为空
        })
    }
    
    /// 初始化OCR服务
    pub async fn initialize(&mut self) -> AppResult<()> { // initialize()异步方法初始化OCR服务
        log::info!("初始化OCR服务..."); // 记录初始化开始日志
        
        // 获取用户偏好的模型
        let preferred_model = self.config_manager.get_config().preferred_model; // 获取用户偏好模型
        
        // 检查模型是否可用
        if !self.engine_manager.model_manager().is_model_available(preferred_model) { // 检查模型是否可用
            log::warn!("用户偏好的模型 {:?} 不可用，尝试使用推荐模型", preferred_model); // 记录警告日志
            
            // 获取推荐模型列表
            let recommended_models = self.engine_manager.model_manager().get_recommended_models(); // 获取推荐模型列表
            
            // 查找第一个可用的推荐模型
            let available_model = recommended_models.iter() // 遍历推荐模型
                .find(|&&model| self.engine_manager.model_manager().is_model_available(model)) // 查找可用模型
                .copied(); // 复制模型类型
            
            if let Some(model) = available_model { // 检查是否找到可用模型
                log::info!("使用推荐模型: {:?}", model); // 记录使用推荐模型日志
                self.engine_manager.model_manager_mut().set_current_model(model)?; // 设置当前模型
            } else {
                return Err(crate::errors::AppError::InternalError("没有可用的OCR模型".to_string())); // 返回没有可用模型错误
            }
        } else {
            self.engine_manager.model_manager_mut().set_current_model(preferred_model)?; // 设置用户偏好模型
        }
        
        // 创建并初始化PaddleOCR引擎
        let mut paddle_engine = Box::new(PaddleOcrEngine::new()); // 创建PaddleOCR引擎
        let engine_config = self.config_manager.get_config().engine_config.clone(); // 获取引擎配置
        paddle_engine.initialize(&engine_config).await?; // 初始化引擎
        
        self.current_engine = Some(paddle_engine); // 设置当前引擎
        
        log::info!("OCR服务初始化完成"); // 记录初始化完成日志
        Ok(()) // 返回成功
    }
    
    /// 识别图像中的文本
    pub async fn recognize_image<P: AsRef<Path>>(&self, image_path: P) -> AppResult<OcrResult> { // recognize_image()异步方法识别图像文本
        let engine = self.current_engine.as_ref() // 获取当前引擎引用
            .ok_or_else(|| crate::errors::AppError::InternalError("OCR引擎未初始化".to_string()))?; // 处理引擎未初始化错误
        
        engine.recognize_image(image_path).await // 调用引擎识别方法
    }
    
    /// 识别图像数据中的文本
    pub async fn recognize_image_data(&self, image_data: &[u8]) -> AppResult<OcrResult> { // recognize_image_data()异步方法识别图像数据文本
        let engine = self.current_engine.as_ref() // 获取当前引擎引用
            .ok_or_else(|| crate::errors::AppError::InternalError("OCR引擎未初始化".to_string()))?; // 处理引擎未初始化错误
        
        engine.recognize_image_data(image_data).await // 调用引擎识别方法
    }
    
    /// 批量识别图像
    pub async fn recognize_batch<P: AsRef<Path> + Send>(&self, image_paths: Vec<P>) -> AppResult<Vec<OcrResult>> { // recognize_batch()异步方法批量识别图像
        let engine = self.current_engine.as_ref() // 获取当前引擎引用
            .ok_or_else(|| crate::errors::AppError::InternalError("OCR引擎未初始化".to_string()))?; // 处理引擎未初始化错误
        
        engine.recognize_batch(image_paths).await // 调用引擎批量识别方法
    }
    
    /// 切换OCR模型
    pub async fn switch_model(&mut self, model_type: OcrModelType) -> AppResult<()> { // switch_model()异步方法切换OCR模型
        // 检查模型是否可用
        if !self.engine_manager.model_manager().is_model_available(model_type) { // 检查模型是否可用
            return Err(crate::errors::AppError::InvalidInput(format!("模型 {:?} 不可用", model_type))); // 返回模型不可用错误
        }
        
        // 更新引擎管理器中的当前模型
        self.engine_manager.model_manager_mut().set_current_model(model_type)?; // 设置当前模型
        
        // 如果有活动引擎，切换模型
        if let Some(ref mut engine) = self.current_engine { // 检查是否有活动引擎
            engine.switch_model(model_type).await?; // 切换引擎模型
        }
        
        // 更新用户配置
        self.config_manager.get_config_mut().preferred_model = model_type; // 更新用户偏好模型
        self.config_manager.save_config()?; // 保存配置
        
        log::info!("已切换到模型: {:?}", model_type); // 记录模型切换日志
        Ok(()) // 返回成功
    }
    
    /// 获取可用模型列表
    pub fn get_available_models(&self) -> Vec<&ModelConfig> { // get_available_models()方法获取可用模型列表
        self.engine_manager.model_manager().get_available_models() // 返回可用模型列表
    }
    
    /// 获取当前模型
    pub fn get_current_model(&self) -> Option<OcrModelType> { // get_current_model()方法获取当前模型
        self.engine_manager.model_manager().get_current_model() // 返回当前模型
    }
    
    /// 获取模型统计信息
    pub fn get_model_statistics(&self) -> ModelStatistics { // get_model_statistics()方法获取模型统计信息
        self.engine_manager.model_manager().get_model_statistics() // 返回模型统计信息
    }
    
    /// 获取引擎信息
    pub fn get_engine_info(&self) -> Option<EngineInfo> { // get_engine_info()方法获取引擎信息
        self.current_engine.as_ref().map(|engine| engine.get_engine_info()) // 返回引擎信息
    }
    
    /// 获取性能统计
    pub fn get_performance_stats(&self) -> &PerformanceStats { // get_performance_stats()方法获取性能统计
        self.engine_manager.get_performance_stats() // 返回性能统计
    }
    
    /// 获取用户配置
    pub fn get_user_config(&self) -> &UserOcrConfig { // get_user_config()方法获取用户配置
        self.config_manager.get_config() // 返回用户配置
    }
    
    /// 更新用户配置
    pub fn update_user_config(&mut self, config: UserOcrConfig) -> AppResult<()> { // update_user_config()方法更新用户配置
        self.config_manager.update_config(config) // 更新配置
    }
    
    /// 验证模型完整性
    pub async fn verify_model(&mut self, model_type: OcrModelType) -> AppResult<bool> { // verify_model()异步方法验证模型完整性
        self.engine_manager.model_manager_mut().verify_model(model_type).await // 验证模型
    }
    
    /// 检查是否应该自动下载模型
    pub fn should_auto_download(&self, is_wifi: bool) -> bool { // should_auto_download()方法检查是否应该自动下载模型
        self.config_manager.should_auto_download(is_wifi) // 检查是否应该自动下载
    }
    
    /// 清理OCR服务资源
    pub async fn cleanup(&mut self) -> AppResult<()> { // cleanup()异步方法清理OCR服务资源
        if let Some(ref mut engine) = self.current_engine { // 检查是否有活动引擎
            engine.cleanup().await?; // 清理引擎资源
        }
        
        self.current_engine = None; // 清空当前引擎
        
        log::info!("OCR服务资源已清理"); // 记录资源清理日志
        Ok(()) // 返回成功
    }
}
