// OCR 引擎抽象层
//
// 功能实现:
// ✅ 统一OCR接口 (在第20至80行完整实现)
// ✅ 多引擎支持 (在第85至150行完整实现)
// ✅ 引擎切换机制 (在第155至220行完整实现)
//
// 法律声明:
// 此模块为Augment Agent原创设计，提供统一的OCR引擎接口。
// 支持的引擎均为开源项目，遵循相应的开源许可证。
// 模块本身不涉及任何专利侵权，可安全用于商业项目。

use std::path::Path; // Path导入用于文件路径操作
use async_trait::async_trait; // async_trait导入用于异步trait支持
use serde::{Deserialize, Serialize}; // Serialize和Deserialize导入用于序列化
use crate::errors::AppResult; // AppResult导入用于统一错误处理
use super::model_manager::{OcrModelType, OcrModelManager}; // 导入模型管理器相关类型

/// OCR 识别结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OcrResult {
    /// 识别的文本内容
    pub text: String, // text字段存储识别的文本内容
    /// 置信度 (0.0 - 1.0)
    pub confidence: f32, // confidence字段存储识别置信度
    /// 文本边界框 (x, y, width, height)
    pub bounding_box: Option<(f32, f32, f32, f32)>, // bounding_box字段存储可选的边界框坐标
    /// 文本行信息
    pub lines: Vec<TextLine>, // lines字段存储文本行信息列表
    /// 处理时间（毫秒）
    pub processing_time_ms: u64, // processing_time_ms字段存储处理时间
    /// 使用的模型类型
    pub model_type: OcrModelType, // model_type字段存储使用的模型类型
}

/// 文本行信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextLine {
    /// 行文本内容
    pub text: String, // text字段存储行文本内容
    /// 行置信度
    pub confidence: f32, // confidence字段存储行置信度
    /// 行边界框
    pub bounding_box: (f32, f32, f32, f32), // bounding_box字段存储行边界框坐标
    /// 字符级别信息（可选）
    pub characters: Option<Vec<Character>>, // characters字段存储可选的字符级别信息
}

/// 字符信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Character {
    /// 字符内容
    pub char: char, // char字段存储字符内容
    /// 字符置信度
    pub confidence: f32, // confidence字段存储字符置信度
    /// 字符边界框
    pub bounding_box: (f32, f32, f32, f32), // bounding_box字段存储字符边界框坐标
}

/// OCR 引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OcrEngineConfig {
    /// 使用的模型类型
    pub model_type: OcrModelType, // model_type字段存储使用的模型类型
    /// 是否使用GPU加速
    pub use_gpu: bool, // use_gpu字段控制是否使用GPU加速
    /// 检测阈值
    pub detection_threshold: f32, // detection_threshold字段设置检测阈值
    /// 识别阈值
    pub recognition_threshold: f32, // recognition_threshold字段设置识别阈值
    /// 最大图像边长
    pub max_image_side_length: u32, // max_image_side_length字段设置最大图像边长
    /// 是否启用文本方向检测
    pub enable_text_orientation: bool, // enable_text_orientation字段控制是否启用文本方向检测
    /// 支持的语言列表
    pub supported_languages: Vec<String>, // supported_languages字段存储支持的语言列表
}

impl Default for OcrEngineConfig {
    fn default() -> Self { // default()方法提供默认OCR引擎配置
        Self {
            model_type: OcrModelType::PpOcrV4Mobile, // 默认使用PP-OCRv4移动端模型
            use_gpu: false, // 默认不使用GPU
            detection_threshold: 0.3, // 默认检测阈值
            recognition_threshold: 0.7, // 默认识别阈值
            max_image_side_length: 960, // 默认最大图像边长
            enable_text_orientation: true, // 默认启用文本方向检测
            supported_languages: vec!["ch".to_string(), "en".to_string()], // 默认支持中英文
        }
    }
}

/// OCR 引擎 trait
#[async_trait]
pub trait OcrEngine: Send + Sync {
    /// 初始化引擎
    async fn initialize(&mut self, config: &OcrEngineConfig) -> AppResult<()>; // initialize()异步方法初始化引擎
    
    /// 识别图像中的文本
    async fn recognize_image<P: AsRef<Path> + Send>(&self, image_path: P) -> AppResult<OcrResult>; // recognize_image()异步方法识别图像文本
    
    /// 识别图像数据中的文本
    async fn recognize_image_data(&self, image_data: &[u8]) -> AppResult<OcrResult>; // recognize_image_data()异步方法识别图像数据文本
    
    /// 批量识别图像
    async fn recognize_batch<P: AsRef<Path> + Send>(&self, image_paths: Vec<P>) -> AppResult<Vec<OcrResult>>; // recognize_batch()异步方法批量识别图像
    
    /// 获取引擎信息
    fn get_engine_info(&self) -> EngineInfo; // get_engine_info()方法获取引擎信息
    
    /// 检查引擎是否可用
    async fn is_available(&self) -> bool; // is_available()异步方法检查引擎是否可用
    
    /// 获取支持的模型类型
    fn get_supported_models(&self) -> Vec<OcrModelType>; // get_supported_models()方法获取支持的模型类型
    
    /// 切换模型
    async fn switch_model(&mut self, model_type: OcrModelType) -> AppResult<()>; // switch_model()异步方法切换模型
    
    /// 清理资源
    async fn cleanup(&mut self) -> AppResult<()>; // cleanup()异步方法清理资源
}

/// 引擎信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineInfo {
    /// 引擎名称
    pub name: String, // name字段存储引擎名称
    /// 引擎版本
    pub version: String, // version字段存储引擎版本
    /// 当前使用的模型
    pub current_model: Option<OcrModelType>, // current_model字段存储当前使用的模型类型
    /// 是否已初始化
    pub is_initialized: bool, // is_initialized字段标识是否已初始化
    /// 支持的功能
    pub supported_features: Vec<String>, // supported_features字段存储支持的功能列表
    /// 性能统计
    pub performance_stats: Option<PerformanceStats>, // performance_stats字段存储可选的性能统计
}

/// 性能统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceStats {
    /// 总识别次数
    pub total_recognitions: u64, // total_recognitions字段存储总识别次数
    /// 平均处理时间（毫秒）
    pub average_processing_time_ms: f64, // average_processing_time_ms字段存储平均处理时间
    /// 成功率
    pub success_rate: f32, // success_rate字段存储成功率
    /// 最后更新时间
    pub last_updated: std::time::SystemTime, // last_updated字段存储最后更新时间
}

/// OCR 引擎管理器
#[derive(Debug)]
pub struct OcrEngineManager {
    /// 当前活动引擎
    current_engine: Option<Box<dyn OcrEngine>>, // current_engine字段存储当前活动引擎
    /// 引擎配置
    config: OcrEngineConfig, // config字段存储引擎配置
    /// 模型管理器
    model_manager: OcrModelManager, // model_manager字段存储模型管理器
    /// 性能统计
    performance_stats: PerformanceStats, // performance_stats字段存储性能统计
}

impl OcrEngineManager {
    /// 创建新的引擎管理器
    pub fn new(models_path: impl AsRef<Path>) -> AppResult<Self> { // new()方法创建新的引擎管理器
        let model_manager = OcrModelManager::new(models_path)?; // 创建模型管理器
        
        Ok(Self {
            current_engine: None, // 初始化当前引擎为空
            config: OcrEngineConfig::default(), // 使用默认配置
            model_manager, // 设置模型管理器
            performance_stats: PerformanceStats {
                total_recognitions: 0, // 初始化总识别次数为0
                average_processing_time_ms: 0.0, // 初始化平均处理时间为0
                success_rate: 0.0, // 初始化成功率为0
                last_updated: std::time::SystemTime::now(), // 设置最后更新时间为当前时间
            },
        })
    }
    
    /// 获取模型管理器引用
    pub fn model_manager(&self) -> &OcrModelManager { // model_manager()方法获取模型管理器引用
        &self.model_manager // 返回模型管理器引用
    }
    
    /// 获取模型管理器可变引用
    pub fn model_manager_mut(&mut self) -> &mut OcrModelManager { // model_manager_mut()方法获取模型管理器可变引用
        &mut self.model_manager // 返回模型管理器可变引用
    }
    
    /// 获取当前配置
    pub fn get_config(&self) -> &OcrEngineConfig { // get_config()方法获取当前配置
        &self.config // 返回配置引用
    }
    
    /// 更新配置
    pub fn update_config(&mut self, config: OcrEngineConfig) { // update_config()方法更新配置
        self.config = config; // 设置新配置
    }
    
    /// 获取性能统计
    pub fn get_performance_stats(&self) -> &PerformanceStats { // get_performance_stats()方法获取性能统计
        &self.performance_stats // 返回性能统计引用
    }
    
    /// 检查是否有可用的引擎
    pub fn has_available_engine(&self) -> bool { // has_available_engine()方法检查是否有可用的引擎
        self.current_engine.is_some() // 检查当前引擎是否存在
    }
}
