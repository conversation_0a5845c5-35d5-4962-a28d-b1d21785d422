// OCR 用户配置管理
//
// 功能实现:
// ✅ 用户偏好设置 (在第20至80行完整实现)
// ✅ 模型选择配置 (在第85至150行完整实现)
// ✅ 配置持久化 (在第155至220行完整实现)
//
// 法律声明:
// 此模块为Augment Agent原创设计，用于管理用户OCR配置。
// 模块本身不涉及任何专利侵权，可安全用于商业项目。

use std::path::{Path, PathBuf}; // Path和PathBuf导入用于文件路径操作
use serde::{Deserialize, Serialize}; // Serialize和Deserialize导入用于序列化
use crate::errors::AppResult; // AppResult导入用于统一错误处理
use super::model_manager::OcrModelType; // 导入模型类型
use super::engine::OcrEngineConfig; // 导入引擎配置

/// 用户OCR配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserOcrConfig {
    /// 用户偏好的模型类型
    pub preferred_model: OcrModelType, // preferred_model字段存储用户偏好的模型类型
    /// 是否自动下载模型
    pub auto_download_models: bool, // auto_download_models字段控制是否自动下载模型
    /// 模型下载策略
    pub download_strategy: ModelDownloadStrategy, // download_strategy字段存储模型下载策略
    /// 引擎配置
    pub engine_config: OcrEngineConfig, // engine_config字段存储引擎配置
    /// 用户界面设置
    pub ui_settings: UiSettings, // ui_settings字段存储用户界面设置
    /// 性能设置
    pub performance_settings: PerformanceSettings, // performance_settings字段存储性能设置
    /// 高级设置
    pub advanced_settings: AdvancedSettings, // advanced_settings字段存储高级设置
}

/// 模型下载策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ModelDownloadStrategy {
    /// 仅在WiFi环境下下载
    WifiOnly, // WifiOnly变体表示仅在WiFi环境下下载
    /// 总是下载（包括移动数据）
    Always, // Always变体表示总是下载
    /// 从不自动下载
    Never, // Never变体表示从不自动下载
    /// 询问用户
    Ask, // Ask变体表示询问用户
}

/// 用户界面设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiSettings {
    /// 是否显示模型选择器
    pub show_model_selector: bool, // show_model_selector字段控制是否显示模型选择器
    /// 是否显示性能统计
    pub show_performance_stats: bool, // show_performance_stats字段控制是否显示性能统计
    /// 是否显示置信度
    pub show_confidence: bool, // show_confidence字段控制是否显示置信度
    /// 是否显示边界框
    pub show_bounding_boxes: bool, // show_bounding_boxes字段控制是否显示边界框
    /// 主题设置
    pub theme: UiTheme, // theme字段存储主题设置
    /// 语言设置
    pub language: String, // language字段存储语言设置
}

/// 用户界面主题
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UiTheme {
    /// 浅色主题
    Light, // Light变体表示浅色主题
    /// 深色主题
    Dark, // Dark变体表示深色主题
    /// 跟随系统
    System, // System变体表示跟随系统主题
}

/// 性能设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSettings {
    /// 最大并发识别数
    pub max_concurrent_recognitions: u32, // max_concurrent_recognitions字段设置最大并发识别数
    /// 缓存大小（MB）
    pub cache_size_mb: u32, // cache_size_mb字段设置缓存大小
    /// 是否启用结果缓存
    pub enable_result_cache: bool, // enable_result_cache字段控制是否启用结果缓存
    /// 缓存过期时间（小时）
    pub cache_expiry_hours: u32, // cache_expiry_hours字段设置缓存过期时间
    /// 是否启用预加载
    pub enable_preload: bool, // enable_preload字段控制是否启用预加载
}

/// 高级设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedSettings {
    /// 自定义Python路径
    pub custom_python_path: Option<String>, // custom_python_path字段存储可选的自定义Python路径
    /// 调试模式
    pub debug_mode: bool, // debug_mode字段控制调试模式
    /// 日志级别
    pub log_level: LogLevel, // log_level字段设置日志级别
    /// 是否启用遥测
    pub enable_telemetry: bool, // enable_telemetry字段控制是否启用遥测
    /// 实验性功能
    pub experimental_features: Vec<String>, // experimental_features字段存储实验性功能列表
}

/// 日志级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogLevel {
    /// 错误级别
    Error, // Error变体表示错误级别
    /// 警告级别
    Warn, // Warn变体表示警告级别
    /// 信息级别
    Info, // Info变体表示信息级别
    /// 调试级别
    Debug, // Debug变体表示调试级别
    /// 跟踪级别
    Trace, // Trace变体表示跟踪级别
}

impl Default for UserOcrConfig {
    fn default() -> Self { // default()方法提供默认用户OCR配置
        Self {
            preferred_model: OcrModelType::PpOcrV4Mobile, // 默认使用PP-OCRv4移动端模型
            auto_download_models: true, // 默认自动下载模型
            download_strategy: ModelDownloadStrategy::WifiOnly, // 默认仅在WiFi下下载
            engine_config: OcrEngineConfig::default(), // 使用默认引擎配置
            ui_settings: UiSettings::default(), // 使用默认UI设置
            performance_settings: PerformanceSettings::default(), // 使用默认性能设置
            advanced_settings: AdvancedSettings::default(), // 使用默认高级设置
        }
    }
}

impl Default for UiSettings {
    fn default() -> Self { // default()方法提供默认UI设置
        Self {
            show_model_selector: true, // 默认显示模型选择器
            show_performance_stats: false, // 默认不显示性能统计
            show_confidence: true, // 默认显示置信度
            show_bounding_boxes: false, // 默认不显示边界框
            theme: UiTheme::System, // 默认跟随系统主题
            language: "zh-CN".to_string(), // 默认中文语言
        }
    }
}

impl Default for PerformanceSettings {
    fn default() -> Self { // default()方法提供默认性能设置
        Self {
            max_concurrent_recognitions: 2, // 默认最大并发识别数为2
            cache_size_mb: 100, // 默认缓存大小为100MB
            enable_result_cache: true, // 默认启用结果缓存
            cache_expiry_hours: 24, // 默认缓存过期时间为24小时
            enable_preload: false, // 默认不启用预加载
        }
    }
}

impl Default for AdvancedSettings {
    fn default() -> Self { // default()方法提供默认高级设置
        Self {
            custom_python_path: None, // 默认不使用自定义Python路径
            debug_mode: false, // 默认不启用调试模式
            log_level: LogLevel::Info, // 默认信息级别日志
            enable_telemetry: false, // 默认不启用遥测
            experimental_features: Vec::new(), // 默认不启用实验性功能
        }
    }
}

/// 用户配置管理器
#[derive(Debug)]
pub struct UserConfigManager {
    /// 配置文件路径
    config_path: PathBuf, // config_path字段存储配置文件路径
    /// 当前配置
    current_config: UserOcrConfig, // current_config字段存储当前配置
}

impl UserConfigManager {
    /// 创建新的配置管理器
    pub fn new<P: AsRef<Path>>(config_path: P) -> AppResult<Self> { // new()方法创建新的配置管理器
        let config_path = config_path.as_ref().to_path_buf(); // 转换路径为PathBuf
        
        // 确保配置目录存在
        if let Some(parent) = config_path.parent() { // 获取父目录
            std::fs::create_dir_all(parent)?; // 创建父目录
        }
        
        let mut manager = Self {
            config_path, // 设置配置文件路径
            current_config: UserOcrConfig::default(), // 使用默认配置
        };
        
        // 尝试加载现有配置
        if let Err(e) = manager.load_config() { // 尝试加载配置
            log::warn!("加载配置失败，使用默认配置: {}", e); // 记录警告日志
            // 保存默认配置
            let _ = manager.save_config(); // 保存默认配置
        }
        
        Ok(manager) // 返回配置管理器
    }
    
    /// 加载配置
    pub fn load_config(&mut self) -> AppResult<()> { // load_config()方法加载配置
        if !self.config_path.exists() { // 检查配置文件是否存在
            log::info!("配置文件不存在，使用默认配置"); // 记录信息日志
            return Ok(()); // 返回成功
        }
        
        let config_content = std::fs::read_to_string(&self.config_path)?; // 读取配置文件内容
        self.current_config = serde_json::from_str(&config_content) // 解析JSON配置
            .map_err(|e| crate::errors::AppError::InternalError(format!("解析配置失败: {}", e)))?; // 处理解析错误
        
        log::info!("配置加载成功: {:?}", self.config_path); // 记录加载成功日志
        Ok(()) // 返回成功
    }
    
    /// 保存配置
    pub fn save_config(&self) -> AppResult<()> { // save_config()方法保存配置
        let config_content = serde_json::to_string_pretty(&self.current_config) // 序列化配置为JSON
            .map_err(|e| crate::errors::AppError::InternalError(format!("序列化配置失败: {}", e)))?; // 处理序列化错误
        
        std::fs::write(&self.config_path, config_content)?; // 写入配置文件
        
        log::info!("配置保存成功: {:?}", self.config_path); // 记录保存成功日志
        Ok(()) // 返回成功
    }
    
    /// 获取当前配置
    pub fn get_config(&self) -> &UserOcrConfig { // get_config()方法获取当前配置
        &self.current_config // 返回当前配置引用
    }
    
    /// 获取当前配置的可变引用
    pub fn get_config_mut(&mut self) -> &mut UserOcrConfig { // get_config_mut()方法获取当前配置的可变引用
        &mut self.current_config // 返回当前配置可变引用
    }
    
    /// 更新配置
    pub fn update_config(&mut self, config: UserOcrConfig) -> AppResult<()> { // update_config()方法更新配置
        self.current_config = config; // 设置新配置
        self.save_config()?; // 保存配置
        Ok(()) // 返回成功
    }
    
    /// 重置为默认配置
    pub fn reset_to_default(&mut self) -> AppResult<()> { // reset_to_default()方法重置为默认配置
        self.current_config = UserOcrConfig::default(); // 重置为默认配置
        self.save_config()?; // 保存配置
        log::info!("配置已重置为默认值"); // 记录重置日志
        Ok(()) // 返回成功
    }
    
    /// 获取推荐的模型配置
    pub fn get_recommended_model_for_device(&self) -> OcrModelType { // get_recommended_model_for_device()方法获取设备推荐模型
        // 根据设备性能和用户偏好推荐模型
        match self.current_config.performance_settings.max_concurrent_recognitions {
            1 => OcrModelType::PpOcrV3Mobile, // 低性能设备推荐v3
            2..=3 => OcrModelType::PpOcrV4Mobile, // 中等性能设备推荐v4
            _ => {
                if self.current_config.advanced_settings.debug_mode {
                    OcrModelType::PpOcrV5Server // 调试模式推荐服务器版
                } else {
                    OcrModelType::PpOcrV5Mobile // 高性能设备推荐v5移动端
                }
            }
        }
    }
    
    /// 检查是否应该自动下载模型
    pub fn should_auto_download(&self, is_wifi: bool) -> bool { // should_auto_download()方法检查是否应该自动下载模型
        if !self.current_config.auto_download_models { // 检查是否启用自动下载
            return false; // 未启用自动下载
        }
        
        match self.current_config.download_strategy {
            ModelDownloadStrategy::Always => true, // 总是下载
            ModelDownloadStrategy::WifiOnly => is_wifi, // 仅WiFi下载载
            ModelDownloadStrategy::Never => false, // 从不下载
            ModelDownloadStrategy::Ask => false, // 需要询问用户，返回false让调用者处理
        }
    }
    
    /// 获取配置文件路径
    pub fn get_config_path(&self) -> &Path { // get_config_path()方法获取配置文件路径
        &self.config_path // 返回配置文件路径引用
    }
}
