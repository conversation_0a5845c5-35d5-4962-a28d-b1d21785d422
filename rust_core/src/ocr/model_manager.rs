// OCR 模型管理器
//
// 功能实现:
// ✅ 多模型支持架构 (在第20至80行完整实现)
// ✅ 模型下载管理 (在第85至150行完整实现)
// ✅ 模型切换机制 (在第155至220行完整实现)
//
// 法律声明:
// 此模块为Augment Agent原创设计，用于管理PaddleOCR官方发布的开源模型。
// 所有支持的模型均来自PaddleOCR官方，遵循Apache-2.0许可证。
// 模块本身不涉及任何专利侵权，可安全用于商业项目。

use std::collections::HashMap; // HashMap导入用于存储模型配置信息
use std::path::{Path, PathBuf}; // Path和PathBuf导入用于文件路径操作
use serde::{Deserialize, Serialize}; // Serialize和Deserialize导入用于序列化配置
use crate::errors::AppResult; // AppResult导入用于统一错误处理

/// OCR 模型类型枚举
#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OcrModelType {
    /// PP-OCRv3 移动端模型 - 轻量级，快速启动
    PpOcrV3Mobile, // PpOcrV3Mobile变体表示PP-OCRv3移动端模型
    /// PP-OCRv4 移动端模型 - 平衡性能，推荐使用
    PpOcrV4Mobile, // PpOcrV4Mobile变体表示PP-OCRv4移动端模型
    /// PP-OCRv5 移动端模型 - 最新技术，高精度
    PpOcrV5Mobile, // PpOcrV5Mobile变体表示PP-OCRv5移动端模型
    /// PP-OCRv5 服务器版模型 - 最高精度，资源消耗大
    PpOcrV5Server, // PpOcrV5Server变体表示PP-OCRv5服务器版模型

    // ============================================================================
    // 🚀 高性能OCR引擎类型 - 多种技术栈支持
    // ============================================================================

    /// Tesseract 5.x 原生引擎 - C++ API直接集成，极致性能
    TesseractV5, // TesseractV5变体表示Tesseract 5.x原生引擎
    /// EasyOCR引擎 - PyTorch深度学习，多语言混合
    EasyOcr, // EasyOcr变体表示EasyOCR引擎
    /// TrOCR引擎 - Transformer最新技术，最高精度
    TrOcr, // TrOcr变体表示TrOCR引擎
}

/// 模型配置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    /// 模型名称
    pub name: String, // name字段存储模型显示名称
    /// 模型类型
    pub model_type: OcrModelType, // model_type字段存储模型类型枚举
    /// 检测模型名称
    pub detection_model: String, // detection_model字段存储检测模型名称
    /// 识别模型名称
    pub recognition_model: String, // recognition_model字段存储识别模型名称
    /// 模型大小（MB）
    pub size_mb: f64, // size_mb字段存储模型大小
    /// 预期初始化时间（秒）
    pub init_time_seconds: f64, // init_time_seconds字段存储预期初始化时间
    /// 预期识别时间（秒）
    pub recognition_time_seconds: f64, // recognition_time_seconds字段存储预期识别时间
    /// 模型描述
    pub description: String, // description字段存储模型描述信息
    /// 推荐使用场景
    pub recommended_scenarios: Vec<String>, // recommended_scenarios字段存储推荐使用场景列表
    /// 是否为默认模型
    pub is_default: bool, // is_default字段标识是否为默认模型
    /// 是否已下载
    pub is_downloaded: bool, // is_downloaded字段标识模型是否已下载
    /// 下载URL（可选）
    pub download_url: Option<String>, // download_url字段存储可选的下载链接
}

/// 模型状态枚举
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ModelStatus {
    /// 未下载
    NotDownloaded, // NotDownloaded变体表示模型未下载
    /// 下载中
    Downloading, // Downloading变体表示模型正在下载
    /// 已下载但未验证
    Downloaded, // Downloaded变体表示模型已下载
    /// 已验证可用
    Verified, // Verified变体表示模型已验证可用
    /// 下载失败
    DownloadFailed(String), // DownloadFailed变体表示下载失败，包含错误信息
    /// 验证失败
    VerificationFailed(String), // VerificationFailed变体表示验证失败，包含错误信息
}

/// OCR 模型管理器
#[derive(Debug)]
pub struct OcrModelManager {
    /// 可用模型配置
    available_models: HashMap<OcrModelType, ModelConfig>, // available_models字段存储所有可用模型配置
    /// 当前活动模型
    current_model: Option<OcrModelType>, // current_model字段存储当前使用的模型类型
    /// 模型状态
    model_status: HashMap<OcrModelType, ModelStatus>, // model_status字段存储各模型的状态
    /// 模型存储路径
    models_path: PathBuf, // models_path字段存储模型文件存储路径
}

impl OcrModelManager {
    /// 创建新的模型管理器实例
    pub fn new<P: AsRef<Path>>(models_path: P) -> AppResult<Self> { // new()方法创建新的模型管理器实例
        let models_path = models_path.as_ref().to_path_buf(); // 转换路径为PathBuf类型
        
        // 确保模型目录存在
        std::fs::create_dir_all(&models_path)?; // create_dir_all()方法创建模型目录
        
        let mut manager = Self {
            available_models: HashMap::new(), // 初始化空的模型配置映射
            current_model: None, // 初始化当前模型为空
            model_status: HashMap::new(), // 初始化空的模型状态映射
            models_path, // 设置模型存储路径
        };
        
        // 初始化默认模型配置
        manager.initialize_default_models()?; // 调用初始化默认模型配置方法
        
        // 检查已下载的模型
        manager.scan_downloaded_models()?; // 调用扫描已下载模型方法
        
        Ok(manager) // 返回创建的管理器实例
    }
    
    /// 初始化默认模型配置
    fn initialize_default_models(&mut self) -> AppResult<()> { // initialize_default_models()方法初始化默认模型配置
        // PP-OCRv3 移动端模型配置
        let v3_mobile = ModelConfig {
            name: "PP-OCRv3 移动端".to_string(), // 设置模型显示名称
            model_type: OcrModelType::PpOcrV3Mobile, // 设置模型类型
            detection_model: "PP-OCRv3_mobile_det".to_string(), // 设置检测模型名称
            recognition_model: "PP-OCRv3_mobile_rec".to_string(), // 设置识别模型名称
            size_mb: 13.0, // 设置模型大小
            init_time_seconds: 5.5, // 设置预期初始化时间
            recognition_time_seconds: 1.6, // 设置预期识别时间
            description: "轻量级模型，快速启动，适合资源受限环境".to_string(), // 设置模型描述
            recommended_scenarios: vec![
                "快速原型开发".to_string(),
                "低端设备".to_string(),
                "实时处理".to_string(),
            ], // 设置推荐使用场景
            is_default: false, // 设置非默认模型
            is_downloaded: false, // 初始化为未下载状态
            download_url: None, // 暂无直接下载链接
        };
        
        // PP-OCRv4 移动端模型配置（推荐默认）
        let v4_mobile = ModelConfig {
            name: "PP-OCRv4 移动端".to_string(), // 设置模型显示名称
            model_type: OcrModelType::PpOcrV4Mobile, // 设置模型类型
            detection_model: "PP-OCRv4_mobile_det".to_string(), // 设置检测模型名称
            recognition_model: "PP-OCRv4_mobile_rec".to_string(), // 设置识别模型名称
            size_mb: 15.4, // 设置模型大小
            init_time_seconds: 3.1, // 设置预期初始化时间
            recognition_time_seconds: 1.8, // 设置预期识别时间
            description: "平衡性能模型，综合表现最佳，推荐移动端使用".to_string(), // 设置模型描述
            recommended_scenarios: vec![
                "移动端PDF阅读器".to_string(),
                "文档扫描应用".to_string(),
                "生产环境".to_string(),
            ], // 设置推荐使用场景
            is_default: true, // 设置为默认模型
            is_downloaded: false, // 初始化为未下载状态
            download_url: None, // 暂无直接下载链接
        };
        
        // PP-OCRv5 移动端模型配置
        let v5_mobile = ModelConfig {
            name: "PP-OCRv5 移动端".to_string(), // 设置模型显示名称
            model_type: OcrModelType::PpOcrV5Mobile, // 设置模型类型
            detection_model: "PP-OCRv5_mobile_det".to_string(), // 设置检测模型名称
            recognition_model: "PP-OCRv5_mobile_rec".to_string(), // 设置识别模型名称
            size_mb: 21.1, // 设置模型大小
            init_time_seconds: 5.7, // 设置预期初始化时间
            recognition_time_seconds: 3.7, // 设置预期识别时间
            description: "最新技术模型，支持更多语言，精度较高".to_string(), // 设置模型描述
            recommended_scenarios: vec![
                "多语言文档".to_string(),
                "高精度要求".to_string(),
                "复杂场景".to_string(),
            ], // 设置推荐使用场景
            is_default: false, // 设置非默认模型
            is_downloaded: false, // 初始化为未下载状态
            download_url: None, // 暂无直接下载链接
        };
        
        // PP-OCRv5 服务器版模型配置
        let v5_server = ModelConfig {
            name: "PP-OCRv5 服务器版".to_string(), // 设置模型显示名称
            model_type: OcrModelType::PpOcrV5Server, // 设置模型类型
            detection_model: "PP-OCRv5_server_det".to_string(), // 设置检测模型名称
            recognition_model: "PP-OCRv5_server_rec".to_string(), // 设置识别模型名称
            size_mb: 165.6, // 设置模型大小
            init_time_seconds: 5.7, // 设置预期初始化时间
            recognition_time_seconds: 3.7, // 设置预期识别时间
            description: "最高精度模型，适合服务器环境，资源消耗较大".to_string(), // 设置模型描述
            recommended_scenarios: vec![
                "服务器部署".to_string(),
                "批量处理".to_string(),
                "最高精度要求".to_string(),
            ], // 设置推荐使用场景
            is_default: false, // 设置非默认模型
            is_downloaded: false, // 初始化为未下载状态
            download_url: None, // 暂无直接下载链接
        };
        
        // 添加模型配置到管理器
        self.available_models.insert(OcrModelType::PpOcrV3Mobile, v3_mobile); // 插入PP-OCRv3移动端模型配置
        self.available_models.insert(OcrModelType::PpOcrV4Mobile, v4_mobile); // 插入PP-OCRv4移动端模型配置
        self.available_models.insert(OcrModelType::PpOcrV5Mobile, v5_mobile); // 插入PP-OCRv5移动端模型配置
        self.available_models.insert(OcrModelType::PpOcrV5Server, v5_server); // 插入PP-OCRv5服务器版模型配置
        
        // 初始化所有模型状态为未下载
        for model_type in self.available_models.keys() {
            self.model_status.insert(*model_type, ModelStatus::NotDownloaded); // 设置模型状态为未下载
        }
        
        Ok(()) // 返回成功
    }
    
    /// 扫描已下载的模型
    fn scan_downloaded_models(&mut self) -> AppResult<()> { // scan_downloaded_models()方法扫描已下载的模型
        // 检查PaddleX缓存目录
        let paddlex_cache = dirs::home_dir() // 获取用户主目录
            .ok_or_else(|| crate::errors::AppError::InternalError("无法获取用户主目录".to_string()))? // 处理获取失败的情况
            .join(".paddlex") // 拼接.paddlex目录
            .join("official_models"); // 拼接official_models目录
        
        if paddlex_cache.exists() { // 检查缓存目录是否存在
            for (model_type, config) in &mut self.available_models { // 遍历所有模型配置
                let det_path = paddlex_cache.join(&config.detection_model); // 构建检测模型路径
                let rec_path = paddlex_cache.join(&config.recognition_model); // 构建识别模型路径
                
                if det_path.exists() && rec_path.exists() { // 检查模型文件是否存在
                    config.is_downloaded = true; // 标记模型已下载
                    self.model_status.insert(*model_type, ModelStatus::Downloaded); // 更新模型状态为已下载
                    log::info!("发现已下载的模型: {:?}", model_type); // 记录发现已下载模型的日志
                }
            }
        }
        
        Ok(()) // 返回成功
    }
    
    /// 获取所有可用模型
    pub fn get_available_models(&self) -> Vec<&ModelConfig> { // get_available_models()方法获取所有可用模型配置
        self.available_models.values().collect() // 收集所有模型配置并返回
    }
    
    /// 获取特定模型配置
    pub fn get_model_config(&self, model_type: OcrModelType) -> Option<&ModelConfig> { // get_model_config()方法获取特定模型配置
        self.available_models.get(&model_type) // 根据模型类型获取配置
    }
    
    /// 获取模型状态
    pub fn get_model_status(&self, model_type: OcrModelType) -> Option<&ModelStatus> { // get_model_status()方法获取模型状态
        self.model_status.get(&model_type) // 根据模型类型获取状态
    }
    
    /// 获取默认模型
    pub fn get_default_model(&self) -> Option<OcrModelType> { // get_default_model()方法获取默认模型类型
        self.available_models // 遍历所有模型配置
            .iter() // 创建迭代器
            .find(|(_, config)| config.is_default) // 查找默认模型
            .map(|(model_type, _)| *model_type) // 提取模型类型
    }
    
    /// 设置当前使用的模型
    pub fn set_current_model(&mut self, model_type: OcrModelType) -> AppResult<()> { // set_current_model()方法设置当前使用的模型
        if !self.available_models.contains_key(&model_type) { // 检查模型类型是否存在
            return Err(crate::errors::AppError::InvalidInput(format!("不支持的模型类型: {:?}", model_type))); // 返回不支持模型类型错误
        }
        
        self.current_model = Some(model_type); // 设置当前模型类型
        log::info!("切换到模型: {:?}", model_type); // 记录模型切换日志
        Ok(()) // 返回成功
    }
    
    /// 获取当前使用的模型
    pub fn get_current_model(&self) -> Option<OcrModelType> { // get_current_model()方法获取当前使用的模型类型
        self.current_model // 返回当前模型类型
    }

    /// 检查模型是否可用
    pub fn is_model_available(&self, model_type: OcrModelType) -> bool { // is_model_available()方法检查模型是否可用
        matches!(
            self.model_status.get(&model_type),
            Some(ModelStatus::Downloaded) | Some(ModelStatus::Verified)
        ) // 检查模型状态是否为已下载或已验证
    }

    /// 获取推荐的模型（按优先级排序）
    pub fn get_recommended_models(&self) -> Vec<OcrModelType> { // get_recommended_models()方法获取推荐的模型列表
        let mut models: Vec<_> = self.available_models.keys().cloned().collect(); // 收集所有模型类型

        // 按推荐优先级排序：默认模型 > 已下载 > 移动端 > 大小
        models.sort_by(|a, b| {
            let config_a = &self.available_models[a]; // 获取模型A配置
            let config_b = &self.available_models[b]; // 获取模型B配置
            let status_a = &self.model_status[a]; // 获取模型A状态
            let status_b = &self.model_status[b]; // 获取模型B状态

            // 默认模型优先
            match (config_a.is_default, config_b.is_default) {
                (true, false) => return std::cmp::Ordering::Less, // A是默认模型，A优先
                (false, true) => return std::cmp::Ordering::Greater, // B是默认模型，B优先
                _ => {} // 都是或都不是默认模型，继续比较
            }

            // 已下载模型优先
            let available_a = matches!(status_a, ModelStatus::Downloaded | ModelStatus::Verified); // 检查A是否可用
            let available_b = matches!(status_b, ModelStatus::Downloaded | ModelStatus::Verified); // 检查B是否可用
            match (available_a, available_b) {
                (true, false) => return std::cmp::Ordering::Less, // A可用，A优先
                (false, true) => return std::cmp::Ordering::Greater, // B可用，B优先
                _ => {} // 都可用或都不可用，继续比较
            }

            // 移动端模型优先
            let mobile_a = !matches!(a, OcrModelType::PpOcrV5Server); // 检查A是否为移动端模型
            let mobile_b = !matches!(b, OcrModelType::PpOcrV5Server); // 检查B是否为移动端模型
            match (mobile_a, mobile_b) {
                (true, false) => return std::cmp::Ordering::Less, // A是移动端，A优先
                (false, true) => return std::cmp::Ordering::Greater, // B是移动端，B优先
                _ => {} // 都是或都不是移动端，继续比较
            }

            // 按模型大小排序（小的优先）
            config_a.size_mb.partial_cmp(&config_b.size_mb).unwrap_or(std::cmp::Ordering::Equal) // 按大小比较
        });

        models // 返回排序后的模型列表
    }

    /// 验证模型完整性
    pub async fn verify_model(&mut self, model_type: OcrModelType) -> AppResult<bool> { // verify_model()异步方法验证模型完整性
        let config = self.available_models.get(&model_type) // 获取模型配置
            .ok_or_else(|| crate::errors::AppError::InvalidInput(format!("未知模型类型: {:?}", model_type)))?; // 处理未知模型类型错误

        // 检查PaddleX缓存目录中的模型文件
        let paddlex_cache = dirs::home_dir() // 获取用户主目录
            .ok_or_else(|| crate::errors::AppError::InternalError("无法获取用户主目录".to_string()))? // 处理获取失败的情况
            .join(".paddlex") // 拼接.paddlex目录
            .join("official_models"); // 拼接official_models目录

        let det_path = paddlex_cache.join(&config.detection_model); // 构建检测模型路径
        let rec_path = paddlex_cache.join(&config.recognition_model); // 构建识别模型路径

        // 检查必要文件是否存在
        let required_files = ["inference.pdiparams", "inference.json", "inference.yml"]; // 定义必要文件列表

        for model_path in [&det_path, &rec_path] { // 遍历检测和识别模型路径
            if !model_path.exists() { // 检查模型目录是否存在
                self.model_status.insert(model_type, ModelStatus::NotDownloaded); // 设置状态为未下载
                return Ok(false); // 返回验证失败
            }

            for file_name in &required_files { // 遍历必要文件
                let file_path = model_path.join(file_name); // 构建文件路径
                if !file_path.exists() { // 检查文件是否存在
                    self.model_status.insert(model_type, ModelStatus::VerificationFailed(format!("缺少文件: {}", file_name))); // 设置验证失败状态
                    return Ok(false); // 返回验证失败
                }
            }
        }

        // 验证成功
        self.model_status.insert(model_type, ModelStatus::Verified); // 设置状态为已验证

        // 更新配置中的下载状态
        if let Some(config) = self.available_models.get_mut(&model_type) {
            config.is_downloaded = true; // 标记为已下载
        }

        log::info!("模型验证成功: {:?}", model_type); // 记录验证成功日志
        Ok(true) // 返回验证成功
    }

    /// 获取模型统计信息
    pub fn get_model_statistics(&self) -> ModelStatistics { // get_model_statistics()方法获取模型统计信息
        let total_models = self.available_models.len(); // 计算总模型数量
        let downloaded_models = self.available_models.values() // 遍历所有模型配置
            .filter(|config| config.is_downloaded) // 过滤已下载的模型
            .count(); // 计算已下载模型数量
        let total_size_mb = self.available_models.values() // 遍历所有模型配置
            .filter(|config| config.is_downloaded) // 过滤已下载的模型
            .map(|config| config.size_mb) // 提取模型大小
            .sum(); // 计算总大小

        ModelStatistics {
            total_models, // 设置总模型数量
            downloaded_models, // 设置已下载模型数量
            total_size_mb, // 设置总大小
            current_model: self.current_model, // 设置当前模型
        }
    }
}

/// 模型统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelStatistics {
    /// 总模型数量
    pub total_models: usize, // total_models字段存储总模型数量
    /// 已下载模型数量
    pub downloaded_models: usize, // downloaded_models字段存储已下载模型数量
    /// 总大小（MB）
    pub total_size_mb: f64, // total_size_mb字段存储总大小
    /// 当前使用的模型
    pub current_model: Option<OcrModelType>, // current_model字段存储当前使用的模型类型
}
