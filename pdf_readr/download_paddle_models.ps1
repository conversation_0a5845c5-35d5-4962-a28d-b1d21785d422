# PaddleOCR 模型下载脚本 (PowerShell 版本)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    PaddleOCR 模型自动下载脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 设置模型目录
$ModelDir = ".\models\paddle_ocr"
$TempDir = ".\temp_downloads"

# 创建临时目录
if (!(Test-Path $TempDir)) {
    New-Item -ItemType Directory -Path $TempDir | Out-Null
}

# 模型下载 URL
$DetModelUrl = "https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_det_infer.tar"
$RecModelUrl = "https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_rec_infer.tar"
$ClsModelUrl = "https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar"
$DictUrl = "https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.6/ppocr/utils/ppocr_keys_v1.txt"

# 下载函数
function Download-File {
    param(
        [string]$Url,
        [string]$OutputPath,
        [string]$Description
    )
    
    Write-Host ""
    Write-Host "📦 下载 $Description..." -ForegroundColor Yellow
    Write-Host "🔗 URL: $Url" -ForegroundColor Gray
    
    try {
        # 使用 Invoke-WebRequest 下载文件
        $ProgressPreference = 'SilentlyContinue'  # 禁用进度条以提高性能
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath -UseBasicParsing
        Write-Host "✅ $Description 下载成功" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $Description 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 解压函数
function Extract-TarFile {
    param(
        [string]$TarFile,
        [string]$DestinationPath,
        [string]$Description
    )
    
    Write-Host "📂 解压 $Description..." -ForegroundColor Yellow
    
    try {
        # 检查是否有 tar 命令
        $tarExists = Get-Command tar -ErrorAction SilentlyContinue
        if ($tarExists) {
            # 使用 tar 命令解压
            tar -xf $TarFile -C $DestinationPath --strip-components=1
            Write-Host "✅ $Description 解压成功" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "⚠️  未找到 tar 命令，请手动解压 $TarFile 到 $DestinationPath" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ $Description 解压失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 验证模型文件
function Verify-Models {
    Write-Host ""
    Write-Host "🔍 验证模型文件..." -ForegroundColor Yellow
    
    $allGood = $true
    
    # 检查检测模型
    if ((Test-Path "$ModelDir\det\inference.pdmodel") -and (Test-Path "$ModelDir\det\inference.pdiparams")) {
        Write-Host "✅ 检测模型文件完整" -ForegroundColor Green
    } else {
        Write-Host "❌ 检测模型文件缺失" -ForegroundColor Red
        $allGood = $false
    }
    
    # 检查识别模型
    if ((Test-Path "$ModelDir\rec\inference.pdmodel") -and (Test-Path "$ModelDir\rec\inference.pdiparams")) {
        Write-Host "✅ 识别模型文件完整" -ForegroundColor Green
    } else {
        Write-Host "❌ 识别模型文件缺失" -ForegroundColor Red
        $allGood = $false
    }
    
    # 检查分类模型
    if ((Test-Path "$ModelDir\cls\inference.pdmodel") -and (Test-Path "$ModelDir\cls\inference.pdiparams")) {
        Write-Host "✅ 分类模型文件完整" -ForegroundColor Green
    } else {
        Write-Host "❌ 分类模型文件缺失" -ForegroundColor Red
        $allGood = $false
    }
    
    # 检查字典文件
    if (Test-Path "$ModelDir\ppocr_keys_v1.txt") {
        Write-Host "✅ 字符字典文件存在" -ForegroundColor Green
    } else {
        Write-Host "❌ 字符字典文件缺失" -ForegroundColor Red
        $allGood = $false
    }
    
    return $allGood
}

# 主下载流程
Write-Host "🚀 开始下载 PaddleOCR 模型..." -ForegroundColor Cyan

# 检查是否已存在模型文件
if (Verify-Models) {
    Write-Host "✅ 模型文件已存在且完整" -ForegroundColor Green
    $response = Read-Host "是否重新下载？(y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        Write-Host "跳过下载" -ForegroundColor Yellow
        exit 0
    }
    Write-Host "🔄 重新下载模型文件..." -ForegroundColor Yellow
}

# 下载检测模型
$detSuccess = Download-File -Url $DetModelUrl -OutputPath "$TempDir\det_model.tar" -Description "检测模型"
if ($detSuccess) {
    $detSuccess = Extract-TarFile -TarFile "$TempDir\det_model.tar" -DestinationPath "$ModelDir\det" -Description "检测模型"
    if ($detSuccess) {
        Remove-Item "$TempDir\det_model.tar" -ErrorAction SilentlyContinue
    }
}

# 下载识别模型
$recSuccess = Download-File -Url $RecModelUrl -OutputPath "$TempDir\rec_model.tar" -Description "识别模型"
if ($recSuccess) {
    $recSuccess = Extract-TarFile -TarFile "$TempDir\rec_model.tar" -DestinationPath "$ModelDir\rec" -Description "识别模型"
    if ($recSuccess) {
        Remove-Item "$TempDir\rec_model.tar" -ErrorAction SilentlyContinue
    }
}

# 下载分类模型
$clsSuccess = Download-File -Url $ClsModelUrl -OutputPath "$TempDir\cls_model.tar" -Description "分类模型"
if ($clsSuccess) {
    $clsSuccess = Extract-TarFile -TarFile "$TempDir\cls_model.tar" -DestinationPath "$ModelDir\cls" -Description "分类模型"
    if ($clsSuccess) {
        Remove-Item "$TempDir\cls_model.tar" -ErrorAction SilentlyContinue
    }
}

# 下载字符字典
$dictSuccess = Download-File -Url $DictUrl -OutputPath "$ModelDir\ppocr_keys_v1.txt" -Description "字符字典"

# 验证下载结果
if (Verify-Models) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "        下载完成！" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "✅ 所有 PaddleOCR 模型下载成功" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 下一步：" -ForegroundColor Yellow
    Write-Host "1. 编译项目: cargo build --features paddle-ocr" -ForegroundColor White
    Write-Host "2. 运行测试: cargo test --features paddle-ocr" -ForegroundColor White
    Write-Host "3. 开始使用 PaddleOCR 进行文本识别" -ForegroundColor White
    Write-Host ""
    
    # 显示模型信息
    Write-Host "📊 模型文件信息:" -ForegroundColor Yellow
    Write-Host "----------------------------------------" -ForegroundColor Gray
    Write-Host "📁 模型目录: $ModelDir" -ForegroundColor White
    
    if (Test-Path "$ModelDir\det") {
        Write-Host "📂 检测模型:" -ForegroundColor White
        Get-ChildItem "$ModelDir\det\inference.*" | ForEach-Object { Write-Host "   $($_.Name)" -ForegroundColor Gray }
    }
    
    if (Test-Path "$ModelDir\rec") {
        Write-Host "📂 识别模型:" -ForegroundColor White
        Get-ChildItem "$ModelDir\rec\inference.*" | ForEach-Object { Write-Host "   $($_.Name)" -ForegroundColor Gray }
    }
    
    if (Test-Path "$ModelDir\cls") {
        Write-Host "📂 分类模型:" -ForegroundColor White
        Get-ChildItem "$ModelDir\cls\inference.*" | ForEach-Object { Write-Host "   $($_.Name)" -ForegroundColor Gray }
    }
    
    if (Test-Path "$ModelDir\ppocr_keys_v1.txt") {
        Write-Host "📚 字符字典:" -ForegroundColor White
        $dictInfo = Get-Item "$ModelDir\ppocr_keys_v1.txt"
        Write-Host "   $($dictInfo.Name) ($($dictInfo.Length) bytes)" -ForegroundColor Gray
    }
} else {
    Write-Host ""
    Write-Host "❌ 模型下载或验证失败" -ForegroundColor Red
    Write-Host "💡 请检查网络连接并重试" -ForegroundColor Yellow
    Write-Host "💡 或者手动下载模型文件到指定目录" -ForegroundColor Yellow
    exit 1
}

# 清理临时文件
if (Test-Path $TempDir) {
    Remove-Item $TempDir -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "🎉 PaddleOCR 模型安装完成！" -ForegroundColor Green
