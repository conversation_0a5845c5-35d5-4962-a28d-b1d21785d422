# PaddleOCR 集成安装指南

## 🎯 PaddleOCR 简介

PaddleOCR 是百度开源的 OCR 工具库，具有以下优势：
- ✅ **优秀的中文识别能力** - 专门优化的中文 OCR 模型
- ✅ **轻量级模型** - 相比其他方案更小的模型体积
- ✅ **高精度识别** - 在中英文混合场景下表现优异
- ✅ **完全开源** - Apache-2.0 许可证，可商用
- ✅ **本地运行** - 无需网络连接，保护数据隐私

## 📋 安装步骤

### 1. 安装 Rust 依赖

我已经为您配置好了 Cargo.toml，包含以下依赖：

```toml
[dependencies]
# PaddleOCR 集成
paddle-ocr = { version = "0.1.0", optional = true }

[features]
# PaddleOCR 特性
paddle-ocr = ["dep:paddle-ocr"]
# 全功能 OCR（包含所有引擎）
full-ocr = ["tesseract", "paddle-ocr", "ocr"]
```

### 2. 下载 PaddleOCR 模型

PaddleOCR 需要预训练模型才能工作。您需要下载以下模型：

#### 方法1：自动下载脚本（推荐）

```bash
# 创建模型目录
mkdir -p models/paddle_ocr/{det,rec,cls}

# 下载检测模型
wget https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_det_infer.tar -O models/paddle_ocr/det.tar
tar -xf models/paddle_ocr/det.tar -C models/paddle_ocr/det/

# 下载识别模型
wget https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_rec_infer.tar -O models/paddle_ocr/rec.tar
tar -xf models/paddle_ocr/rec.tar -C models/paddle_ocr/rec/

# 下载分类模型
wget https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar -O models/paddle_ocr/cls.tar
tar -xf models/paddle_ocr/cls.tar -C models/paddle_ocr/cls/

# 下载字符字典
wget https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.6/ppocr/utils/ppocr_keys_v1.txt -O models/paddle_ocr/ppocr_keys_v1.txt
```

#### 方法2：手动下载

1. 访问 [PaddleOCR 模型库](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.6/doc/doc_ch/models_list.md)
2. 下载以下模型：
   - **检测模型**: `ch_PP-OCRv3_det_infer.tar`
   - **识别模型**: `ch_PP-OCRv3_rec_infer.tar`
   - **分类模型**: `ch_ppocr_mobile_v2.0_cls_infer.tar`
   - **字符字典**: `ppocr_keys_v1.txt`

3. 解压到项目目录：
```
pdf_reader/
├── models/
│   └── paddle_ocr/
│       ├── det/
│       │   ├── inference.pdmodel
│       │   ├── inference.pdiparams
│       │   └── inference.pdiparams.info
│       ├── rec/
│       │   ├── inference.pdmodel
│       │   ├── inference.pdiparams
│       │   └── inference.pdiparams.info
│       ├── cls/
│       │   ├── inference.pdmodel
│       │   ├── inference.pdiparams
│       │   └── inference.pdiparams.info
│       └── ppocr_keys_v1.txt
```

### 3. 安装系统依赖

#### Windows
```bash
# 如果使用 GPU（可选）
# 需要安装 CUDA 和 cuDNN

# CPU 版本无需额外依赖
```

#### macOS
```bash
# 安装必要的系统库
brew install opencv
```

#### Linux (Ubuntu/Debian)
```bash
# 安装 OpenCV 和其他依赖
sudo apt update
sudo apt install libopencv-dev pkg-config

# 如果使用 GPU（可选）
# sudo apt install nvidia-cuda-toolkit
```

### 4. 编译和测试

```bash
# 编译项目（启用 PaddleOCR 特性）
cargo build --features paddle-ocr

# 运行测试
cargo test --features paddle-ocr test_paddle_ocr_integration -- --nocapture

# 运行完整测试
cargo test --features full-ocr -- --nocapture
```

## 🔧 配置选项

### 基础配置

```rust
use crate::ocr::paddle_ocr_engine::{PaddleOCREngine, PaddleOCRConfig};

let config = PaddleOCRConfig {
    model_dir: "./models/paddle_ocr".to_string(),
    use_gpu: false,                    // 是否使用 GPU
    cpu_threads: 4,                    // CPU 线程数
    det_thresh: 0.3,                   // 检测阈值（越低检测越敏感）
    rec_thresh: 0.5,                   // 识别阈值（越高要求越严格）
    max_side_len: 960,                 // 图像最大边长
    use_angle_cls: true,               // 是否使用角度分类
    use_space_char: true,              // 是否识别空格字符
    ..Default::default()
};

let mut engine = PaddleOCREngine::new(config)?;
```

### 高级配置

```rust
// GPU 配置（如果有 NVIDIA GPU）
let gpu_config = PaddleOCRConfig {
    use_gpu: true,
    gpu_id: 0,                         // GPU 设备 ID
    enable_mkldnn: false,              // GPU 模式下通常禁用 MKLDNN
    ..Default::default()
};

// 高精度配置
let high_accuracy_config = PaddleOCRConfig {
    det_thresh: 0.2,                   // 更低的检测阈值
    rec_thresh: 0.7,                   // 更高的识别阈值
    max_side_len: 1280,                // 更大的图像尺寸
    cpu_threads: 8,                    // 更多线程
    ..Default::default()
};

// 快速配置
let fast_config = PaddleOCRConfig {
    det_thresh: 0.5,                   // 更高的检测阈值
    rec_thresh: 0.4,                   // 更低的识别阈值
    max_side_len: 640,                 // 更小的图像尺寸
    cpu_threads: 2,                    // 更少线程
    use_angle_cls: false,              // 禁用角度分类
    ..Default::default()
};
```

## 🧪 使用示例

### 基础使用

```rust
use crate::ocr::paddle_ocr_engine::{PaddleOCREngine, PaddleOCRConfig};
use crate::ocr::engine::{ImageData, ImageFormat};

// 创建引擎
let config = PaddleOCRConfig::default();
let mut engine = PaddleOCREngine::new(config)?;

// 加载图像
let image_data = std::fs::read("test_image.png")?;
let image = ImageData {
    data: image_data,
    width: 800,  // 根据实际图像调整
    height: 600,
    format: ImageFormat::Png,
};

// 执行 OCR
let result = engine.recognize_image(&image)?;

println!("识别文本: {}", result.text);
println!("置信度: {:.3}", result.confidence);
println!("处理时间: {:?}", result.processing_time);
```

### 批量处理

```rust
// 批量处理多个图像
let images = vec![image1, image2, image3];
let results = engine.recognize_batch(&images)?;

for (i, result) in results.iter().enumerate() {
    println!("图像 {}: {}", i + 1, result.text);
}
```

### 与其他 OCR 引擎结合

```rust
use crate::ocr::engine::OCREngine;

// 创建多引擎支持
let mut paddle_engine: Box<dyn OCREngine> = Box::new(PaddleOCREngine::new(config)?);
let mut tesseract_engine: Box<dyn OCREngine> = Box::new(create_tesseract_engine("chi_sim")?);

// 尝试 PaddleOCR，失败时使用 Tesseract
let result = match paddle_engine.recognize_text(&image) {
    Ok(result) if result.confidence_score > 0.7 => result,
    _ => {
        println!("PaddleOCR 结果不理想，尝试 Tesseract...");
        tesseract_engine.recognize_text(&image)?
    }
};
```

## ❗ 常见问题

### 问题1：模型文件缺失
**错误**: `PaddleOCR 初始化失败: 模型文件不存在`

**解决方案**:
- 确保已下载所有必要的模型文件
- 检查模型文件路径是否正确
- 验证模型文件完整性

### 问题2：内存不足
**错误**: `内存分配失败`

**解决方案**:
- 减少 `max_side_len` 参数
- 减少 `cpu_threads` 数量
- 使用批量处理时减少批次大小

### 问题3：识别精度不高
**解决方案**:
- 调整 `det_thresh` 和 `rec_thresh` 参数
- 启用 `use_angle_cls` 处理倾斜文本
- 增加 `max_side_len` 提高图像分辨率
- 对图像进行预处理（去噪、增强对比度）

### 问题4：处理速度慢
**解决方案**:
- 启用 GPU 加速（如果有 NVIDIA GPU）
- 启用 `enable_mkldnn`（CPU 模式）
- 减少 `max_side_len`
- 禁用 `use_angle_cls`

## 🚀 性能优化建议

1. **GPU 加速**: 如果有 NVIDIA GPU，启用 GPU 模式可显著提升速度
2. **图像预处理**: 对图像进行适当的预处理可提高识别精度
3. **参数调优**: 根据具体场景调整检测和识别阈值
4. **批量处理**: 对多个图像使用批量处理可提高整体效率
5. **模型选择**: 根据需求选择合适的模型（速度 vs 精度）

## 📞 获取帮助

如果您在安装或使用过程中遇到问题：

1. 检查模型文件是否完整下载
2. 验证系统依赖是否正确安装
3. 查看日志输出中的详细错误信息
4. 尝试使用不同的配置参数

安装完成后，您就可以享受 PaddleOCR 强大的中文 OCR 能力了！
