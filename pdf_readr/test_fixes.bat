@echo off
echo 正在验证修复的测试...
echo.

echo 1. 测试标题级别识别...
cd rust_core
cargo test --lib test_heading_level_identification --quiet
if %ERRORLEVEL% EQU 0 (
    echo ✅ test_heading_level_identification 通过
) else (
    echo ❌ test_heading_level_identification 失败
)
echo.

echo 2. 测试文本结构分析...
cargo test --lib test_text_structure_analysis --quiet
if %ERRORLEVEL% EQU 0 (
    echo ✅ test_text_structure_analysis 通过
) else (
    echo ❌ test_text_structure_analysis 失败
)
echo.

echo 3. 测试文本重排...
cargo test --lib test_text_reflow --quiet
if %ERRORLEVEL% EQU 0 (
    echo ✅ test_text_reflow 通过
) else (
    echo ❌ test_text_reflow 失败
)
echo.

echo 4. 测试增强错误创建...
cargo test --lib test_enhanced_error_creation --quiet
if %ERRORLEVEL% EQU 0 (
    echo ✅ test_enhanced_error_creation 通过
) else (
    echo ❌ test_enhanced_error_creation 失败
)
echo.

echo 修复验证完成！
cd ..
