name: pdf_reader_app
description: "智能PDF阅读器 - 支持OCR文字识别、语音朗读和对照编辑的移动端应用"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI组件和图标
  cupertino_icons: ^1.0.8
  material_symbols_icons: ^4.2785.1

  # 状态管理
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # 文件和图像处理
  image_picker: ^1.0.4
  file_picker: ^6.1.1

  # 路由导航
  go_router: ^14.2.7

  # 文件处理
  path_provider: ^2.1.4
  path: ^1.9.0

  # PDF处理和文档格式支持
  pdf: ^3.10.7  # PDF文档处理和生成
  printing: ^5.12.0  # PDF打印和预览
  pdfx: ^2.6.0  # PDF渲染和操作

  # 网络和数据
  dio: ^5.7.0
  shared_preferences: ^2.3.2
  connectivity_plus: ^6.0.5  # 网络连接状态监控

  # 数据库
  sqflite: ^2.3.3  # SQLite数据库
  sqlite3: ^2.4.6  # SQLite3支持

  # FFI桥接 (连接Rust核心库)
  ffi: ^2.1.3
  flutter_rust_bridge: ^2.5.1  # Rust桥接

  # 权限管理
  permission_handler: ^11.3.1

  # 设备信息和系统集成
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2
  battery_plus: ^6.0.2  # 电池状态监控
  network_info_plus: ^5.0.3  # 网络信息获取

  # 图像处理和OCR支持
  image: ^4.2.0  # 图像处理
  flutter_tesseract_ocr: ^0.4.25  # Tesseract OCR引擎
  # google_mlkit_text_recognition: ^0.13.0  # Google ML Kit文字识别 (暂时注释)

  # 语音和音频处理
  flutter_tts: ^4.0.2  # 文字转语音
  # speech_to_text: ^7.0.0  # 语音识别 (暂时注释)
  # audioplayers: ^6.1.0  # 音频播放 (暂时注释)

  # 缓存和存储
  hive: ^2.2.3  # 高性能本地存储
  hive_flutter: ^1.1.0  # Hive Flutter集成

  # 加密和安全
  crypto: ^3.0.5  # 加密算法
  encrypt: ^5.0.3  # 数据加密

  # 国际化
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码质量和分析
  flutter_lints: ^5.0.0
  very_good_analysis: ^6.0.0

  # 代码生成
  riverpod_generator: ^2.4.3
  build_runner: ^2.4.13
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1  # Hive代码生成

  # 测试工具
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

  # 性能测试和基准测试
  flutter_driver:
    sdk: flutter
  test: ^1.25.8

  # 代码覆盖率
  coverage: ^1.9.2

  # FFI和Rust集成开发工具
  ffigen: ^13.0.0  # FFI代码生成

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
