/// PDF页面显示组件 (features/pdf_reader/presentation/widgets/pdf_page_widget.dart)
///
/// 功能实现:
/// ✅ PDF页面渲染显示 (完整实现)
/// ✅ 缩放和平移操作 (完整实现)
/// ✅ 加载状态管理 (完整实现)
/// ✅ 错误处理显示 (完整实现)
/// ✅ 手势交互支持 (完整实现)
///
/// 设计原则:
/// - 高性能的图像显示
/// - 流畅的缩放和平移
/// - 响应式的加载状态
/// - 友好的错误提示
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-15

import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/rust_service.dart';
// import '../../../../core/ffi/rust_bridge.dart'; // 未使用的导入

/// PDF页面显示组件
class PDFPageWidget extends ConsumerStatefulWidget {
  final String filePath;
  final int pageNumber;
  final double initialScale;
  final VoidCallback? onTap;
  final ValueChanged<double>? onScaleChanged;

  const PDFPageWidget({
    super.key,
    required this.filePath,
    required this.pageNumber,
    this.initialScale = 1.0,
    this.onTap,
    this.onScaleChanged,
  });

  @override
  ConsumerState<PDFPageWidget> createState() => _PDFPageWidgetState();
}

class _PDFPageWidgetState extends ConsumerState<PDFPageWidget>
    with TickerProviderStateMixin {
  late TransformationController _transformationController;
  late AnimationController _animationController;

  double _currentScale = 1.0;
  ui.Image? _pageImage;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _currentScale = widget.initialScale;
    _transformationController = TransformationController();
    _animationController = AnimationController(
      duration: AnimationConstants.durationNormal,
      vsync: this,
    );

    _loadPage();
  }

  @override
  void didUpdateWidget(PDFPageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.filePath != widget.filePath ||
        oldWidget.pageNumber != widget.pageNumber ||
        oldWidget.initialScale != widget.initialScale) {
      _loadPage();
    }
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _animationController.dispose();
    _pageImage?.dispose();
    super.dispose();
  }

  /// 加载PDF页面
  Future<void> _loadPage() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      final params = PDFPageRenderParams(
        filePath: widget.filePath,
        pageNumber: widget.pageNumber,
        scale: _currentScale,
      );

      final renderResult = await ref.read(pdfPageRenderProvider(params).future);

      // 将图像数据转换为Flutter Image
      final image = await _createImageFromBytes(renderResult.imageData);

      if (mounted) {
        setState(() {
          _pageImage?.dispose();
          _pageImage = image;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = '页面加载失败: $e';
        });
      }
    }
  }

  /// 从字节数据创建图像
  Future<ui.Image> _createImageFromBytes(Uint8List bytes) async {
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  /// 处理缩放变化
  void _onScaleChanged(double scale) {
    if (scale != _currentScale) {
      setState(() {
        _currentScale = scale;
      });

      widget.onScaleChanged?.call(scale);

      // 重新加载页面以获得更高质量的渲染
      _loadPage();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[100],
      child: _buildContent(theme),
    );
  }

  /// 构建内容
  Widget _buildContent(ThemeData theme) {
    if (_isLoading) {
      return _buildLoadingView(theme);
    }

    if (_errorMessage != null) {
      return _buildErrorView(theme);
    }

    if (_pageImage == null) {
      return _buildEmptyView(theme);
    }

    return _buildPageView(theme);
  }

  /// 构建加载视图
  Widget _buildLoadingView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.primary),
          const SizedBox(height: UIConstants.spacingM),
          Text(
            '正在渲染页面 ${widget.pageNumber}...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.spacingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: UIConstants.iconSizeXL,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: UIConstants.spacingM),
            Text(
              '页面加载失败',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: UIConstants.spacingS),
            Text(
              _errorMessage ?? '未知错误',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: UIConstants.spacingL),
            ElevatedButton.icon(
              onPressed: _loadPage,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建空视图
  Widget _buildEmptyView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: UIConstants.iconSizeXL,
            color: theme.colorScheme.onSurface.withOpacity(0.4),
          ),
          const SizedBox(height: UIConstants.spacingM),
          Text(
            '页面内容为空',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建页面视图
  Widget _buildPageView(ThemeData theme) {
    return GestureDetector(
      onTap: widget.onTap,
      child: InteractiveViewer(
        transformationController: _transformationController,
        minScale: PDFConstants.minZoomLevel,
        maxScale: PDFConstants.maxZoomLevel,
        onInteractionUpdate: (details) {
          final scale = _transformationController.value.getMaxScaleOnAxis();
          if ((scale - _currentScale).abs() > 0.1) {
            _onScaleChanged(scale);
          }
        },
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: CustomPaint(
            painter: PDFPagePainter(_pageImage!),
            size: Size.infinite,
          ),
        ),
      ),
    );
  }
}

/// PDF页面绘制器
class PDFPagePainter extends CustomPainter {
  final ui.Image image;

  PDFPagePainter(this.image);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true;

    // 计算图像显示区域
    final imageSize = Size(image.width.toDouble(), image.height.toDouble());
    final targetSize = _calculateTargetSize(imageSize, size);
    final offset = _calculateOffset(targetSize, size);

    final destRect = Rect.fromLTWH(
      offset.dx,
      offset.dy,
      targetSize.width,
      targetSize.height,
    );

    final srcRect = Rect.fromLTWH(
      0,
      0,
      image.width.toDouble(),
      image.height.toDouble(),
    );

    canvas.drawImageRect(image, srcRect, destRect, paint);
  }

  /// 计算目标尺寸（保持宽高比）
  Size _calculateTargetSize(Size imageSize, Size containerSize) {
    final imageAspectRatio = imageSize.width / imageSize.height;
    final containerAspectRatio = containerSize.width / containerSize.height;

    if (imageAspectRatio > containerAspectRatio) {
      // 图像更宽，以容器宽度为准
      final width = containerSize.width;
      final height = width / imageAspectRatio;
      return Size(width, height);
    } else {
      // 图像更高，以容器高度为准
      final height = containerSize.height;
      final width = height * imageAspectRatio;
      return Size(width, height);
    }
  }

  /// 计算偏移量（居中显示）
  Offset _calculateOffset(Size targetSize, Size containerSize) {
    final dx = (containerSize.width - targetSize.width) / 2;
    final dy = (containerSize.height - targetSize.height) / 2;
    return Offset(dx, dy);
  }

  @override
  bool shouldRepaint(PDFPagePainter oldDelegate) {
    return oldDelegate.image != image;
  }
}
