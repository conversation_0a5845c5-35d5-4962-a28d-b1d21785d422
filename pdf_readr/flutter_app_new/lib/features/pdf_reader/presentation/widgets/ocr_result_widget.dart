/// OCR结果显示组件 (features/pdf_reader/presentation/widgets/ocr_result_widget.dart)
///
/// 功能实现:
/// ✅ OCR结果文本显示 (在45至85行完整实现)
/// ✅ 置信度和统计信息 (在90至120行完整实现)
/// ✅ 文本编辑和复制功能 (在125至165行完整实现)
/// ✅ 加载状态管理 (在170至200行完整实现)
/// ✅ 错误处理显示 (在205至240行完整实现)
///
/// 设计原则:
/// - 清晰的文本显示和编辑
/// - 直观的置信度指示
/// - 便捷的文本操作功能
/// - 友好的状态反馈
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-15

import 'package:flutter/material.dart'; // flutter/material.dart导入Material Design组件库
import 'package:flutter/services.dart'; // flutter/services.dart导入系统服务，用于剪贴板操作
import 'package:flutter_riverpod/flutter_riverpod.dart'; // flutter_riverpod导入状态管理库

import '../../../../core/constants/app_constants.dart'; // 导入应用常量定义
import '../../../../core/ffi/rust_bridge.dart'; // 导入FFI桥接
import '../../../../core/services/rust_service.dart'; // 导入Rust服务// OCR结果显示组件

class OCRResultWidget extends ConsumerStatefulWidget {
  // OCRResultWidget类继承ConsumerStatefulWidget，创建有状态的消费者组件
  final Uint8List imageData; // imageData字段存储图像数据，Uint8List类型表示字节数组
  final String language; // language字段存储识别语言，String类型
  final VoidCallback? onClose; // onClose字段存储关闭回调，VoidCallback?表示可空的无参数回调函数

  const OCRResultWidget({
    // 构造函数使用const关键字创建编译时常量
    super.key, // super.key调用父类构造函数传递key参数
    required this.imageData, // required关键字表示必需参数，this.imageData初始化图像数据字段
    this.language = 'auto', // 默认语言为自动检测
    this.onClose, // 可选的关闭回调参数
  });

  @override
  ConsumerState<OCRResultWidget> createState() => _OCRResultWidgetState(); // createState()方法创建State实例，=>箭头函数语法
}

class _OCRResultWidgetState extends ConsumerState<OCRResultWidget> {
  // _OCRResultWidgetState类继承ConsumerState，实现组件状态管理
  late TextEditingController
  _textController; // _textController字段存储文本编辑控制器，late关键字表示延迟初始化
  bool _isEditing = false; // _isEditing字段存储编辑状态，bool类型默认为false

  @override
  void initState() {
    // initState()方法在组件初始化时调用
    super.initState(); // super.initState()调用父类初始化方法
    _textController =
        TextEditingController(); // TextEditingController()创建文本编辑控制器实例
  }

  @override
  void dispose() {
    // dispose()方法在组件销毁时调用
    _textController.dispose(); // dispose()方法释放文本控制器资源
    super.dispose(); // super.dispose()调用父类销毁方法
  }

  @override
  Widget build(BuildContext context) {
    // build()方法构建组件UI，context参数提供构建上下文
    final theme = Theme.of(context); // Theme.of(context)获取当前主题配置
    final params = OCRProcessParams(
      // OCRProcessParams构造函数创建OCR处理参数
      imageData: widget.imageData, // widget.imageData获取组件的图像数据
      language: widget.language, // widget.language获取组件的语言设置
    );

    return Container(
      // Container组件创建容器布局
      decoration: BoxDecoration(
        // BoxDecoration装饰器设置容器样式
        color: theme.colorScheme.surface, // color属性设置背景颜色为主题表面色
        borderRadius: BorderRadius.circular(
          UIConstants.radiusL,
        ), // borderRadius设置圆角，UIConstants.radiusL为大圆角常量
        boxShadow: [
          // boxShadow设置阴影效果
          BoxShadow(
            // BoxShadow创建阴影
            color: Colors.black.withOpacity(
              0.1,
            ), // color设置阴影颜色，withOpacity()方法设置透明度
            blurRadius: 8, // blurRadius设置模糊半径
            offset: const Offset(0, 4), // offset设置阴影偏移量，Offset(0, 4)表示向下偏移4像素
          ),
        ],
      ),
      child: Column(
        // Column组件创建垂直布局
        crossAxisAlignment:
            CrossAxisAlignment.stretch, // crossAxisAlignment设置交叉轴对齐方式为拉伸
        children: [
          // children列表包含子组件
          _buildHeader(theme), // _buildHeader()方法构建头部组件
          Expanded(
            // Expanded组件占用剩余空间
            child: Consumer(
              // Consumer组件监听状态变化
              builder: (context, ref, child) {
                // builder函数构建UI，ref参数提供状态引用
                final ocrAsync = ref.watch(
                  ocrProcessProvider(params),
                ); // ref.watch()监听OCR处理状态

                return ocrAsync.when(
                  // when()方法根据异步状态构建不同UI
                  data: (result) => _buildResultContent(
                    theme,
                    result,
                  ), // data回调处理成功状态，result参数是OCR结果
                  loading: () => _buildLoadingContent(theme), // loading回调处理加载状态
                  error: (error, stack) => _buildErrorContent(
                    theme,
                    error,
                  ), // error回调处理错误状态，error和stack参数提供错误信息
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建头部组件
  Widget _buildHeader(ThemeData theme) {
    // _buildHeader()方法构建头部UI，theme参数提供主题配置
    return Container(
      // Container组件创建头部容器
      padding: const EdgeInsets.all(
        UIConstants.spacingM,
      ), // padding设置内边距，EdgeInsets.all()设置四边相等边距
      decoration: BoxDecoration(
        // BoxDecoration设置容器装饰
        color: theme.colorScheme.primaryContainer, // color设置背景颜色为主题主色容器色
        borderRadius: const BorderRadius.vertical(
          // borderRadius设置垂直方向圆角
          top: Radius.circular(UIConstants.radiusL), // top设置顶部圆角
        ),
      ),
      child: Row(
        // Row组件创建水平布局
        children: [
          // children列表包含子组件
          Icon(
            // Icon组件显示图标
            Icons.text_fields, // Icons.text_fields图标表示文本字段
            color: theme.colorScheme.onPrimaryContainer, // color设置图标颜色
            size: UIConstants.iconSizeM, // size设置图标大小
          ),
          const SizedBox(width: UIConstants.spacingS), // SizedBox组件创建固定宽度间距
          Expanded(
            // Expanded组件占用剩余空间
            child: Text(
              // Text组件显示文本
              'OCR文字识别结果', // 显示的文本内容
              style: theme.textTheme.titleMedium?.copyWith(
                // style设置文本样式，copyWith()方法复制并修改样式
                color: theme.colorScheme.onPrimaryContainer, // color设置文本颜色
                fontWeight: FontWeight.w600, // fontWeight设置字体粗细
              ),
            ),
          ),
          if (widget.onClose != null) // if条件判断是否有关闭回调
            IconButton(
              // IconButton组件创建图标按钮
              icon: Icon(
                // Icon组件显示关闭图标
                Icons.close, // Icons.close关闭图标
                color: theme.colorScheme.onPrimaryContainer, // color设置图标颜色
              ),
              onPressed: widget.onClose, // onPressed设置按钮点击回调
              tooltip: '关闭', // tooltip设置按钮提示文本
            ),
        ],
      ),
    );
  }

  /// 构建结果内容
  Widget _buildResultContent(ThemeData theme, OCRResult result) {
    // _buildResultContent()方法构建结果内容，result参数是OCR识别结果
    if (_textController.text.isEmpty) {
      // isEmpty检查文本控制器是否为空
      _textController.text = result.text; // 设置文本控制器内容为识别结果
    }

    return Padding(
      // Padding组件添加内边距
      padding: const EdgeInsets.all(UIConstants.spacingM), // padding设置四边内边距
      child: Column(
        // Column组件创建垂直布局
        crossAxisAlignment:
            CrossAxisAlignment.start, // crossAxisAlignment设置交叉轴对齐方式为开始
        children: [
          // children列表包含子组件
          _buildStatistics(theme, result), // _buildStatistics()方法构建统计信息
          const SizedBox(height: UIConstants.spacingM), // SizedBox组件创建垂直间距
          _buildTextContent(theme), // _buildTextContent()方法构建文本内容
          const SizedBox(height: UIConstants.spacingM), // SizedBox组件创建垂直间距
          _buildActionButtons(theme), // _buildActionButtons()方法构建操作按钮
        ],
      ),
    );
  }

  /// 构建统计信息
  Widget _buildStatistics(ThemeData theme, OCRResult result) {
    // _buildStatistics()方法构建统计信息UI
    return Container(
      // Container组件创建统计信息容器
      padding: const EdgeInsets.all(UIConstants.spacingM), // padding设置内边距
      decoration: BoxDecoration(
        // BoxDecoration设置容器装饰
        color: theme.colorScheme.surfaceVariant.withOpacity(
          0.5,
        ), // color设置背景颜色，withOpacity()设置透明度
        borderRadius: BorderRadius.circular(
          UIConstants.radiusM,
        ), // borderRadius设置圆角
      ),
      child: Row(
        // Row组件创建水平布局
        mainAxisAlignment:
            MainAxisAlignment.spaceAround, // mainAxisAlignment设置主轴对齐方式为均匀分布
        children: [
          // children列表包含统计项
          _buildStatItem(
            theme,
            '置信度',
            '${(result.confidence * 100).toStringAsFixed(1)}%',
          ), // _buildStatItem()构建置信度统计项
          _buildStatItem(
            theme,
            '字符数',
            '${result.characterCount}',
          ), // _buildStatItem()构建字符数统计项
          _buildStatItem(
            theme,
            '耗时',
            '${result.processingTimeMs}ms',
          ), // _buildStatItem()构建处理时间统计项
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(ThemeData theme, String label, String value) {
    // _buildStatItem()方法构建单个统计项
    return Column(
      // Column组件创建垂直布局
      children: [
        // children列表包含标签和数值
        Text(
          // Text组件显示数值
          value, // value参数是统计数值
          style: theme.textTheme.titleMedium?.copyWith(
            // style设置文本样式
            fontWeight: FontWeight.bold, // fontWeight设置字体粗细为粗体
            color: theme.colorScheme.primary, // color设置文本颜色为主题主色
          ),
        ),
        const SizedBox(height: UIConstants.spacingXS), // SizedBox组件创建小间距
        Text(
          // Text组件显示标签
          label, // label参数是统计标签
          style: theme.textTheme.bodySmall?.copyWith(
            // style设置文本样式
            color: theme.colorScheme.onSurfaceVariant, // color设置文本颜色
          ),
        ),
      ],
    );
  }

  /// 构建文本内容
  Widget _buildTextContent(ThemeData theme) {
    // _buildTextContent()方法构建文本内容区域
    return Expanded(
      // Expanded组件占用剩余空间
      child: Container(
        // Container组件创建文本容器
        decoration: BoxDecoration(
          // BoxDecoration设置容器装饰
          border: Border.all(
            color: theme.colorScheme.outline,
          ), // border设置边框，Border.all()设置四边边框
          borderRadius: BorderRadius.circular(
            UIConstants.radiusM,
          ), // borderRadius设置圆角
        ),
        child: TextField(
          // TextField组件创建文本输入框
          controller: _textController, // controller设置文本控制器
          maxLines: null, // maxLines设置为null表示多行文本
          expands: true, // expands设置为true表示扩展填充
          readOnly: !_isEditing, // readOnly根据编辑状态设置只读属性
          decoration: InputDecoration(
            // InputDecoration设置输入框装饰
            hintText: '识别的文本将显示在这里...', // hintText设置提示文本
            border: InputBorder.none, // border设置为无边框
            contentPadding: const EdgeInsets.all(
              UIConstants.spacingM,
            ), // contentPadding设置内容内边距
          ),
          style: theme.textTheme.bodyMedium, // style设置文本样式
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(ThemeData theme) {
    // _buildActionButtons()方法构建操作按钮区域
    return Row(
      // Row组件创建水平布局
      mainAxisAlignment:
          MainAxisAlignment.spaceEvenly, // mainAxisAlignment设置主轴对齐方式为均匀分布
      children: [
        // children列表包含操作按钮
        ElevatedButton.icon(
          // ElevatedButton.icon组件创建带图标的按钮
          onPressed: _toggleEditing, // onPressed设置按钮点击回调
          icon: Icon(_isEditing ? Icons.save : Icons.edit), // icon根据编辑状态显示不同图标
          label: Text(_isEditing ? '保存' : '编辑'), // label根据编辑状态显示不同文本
        ),
        ElevatedButton.icon(
          // ElevatedButton.icon组件创建复制按钮
          onPressed: _copyToClipboard, // onPressed设置复制回调
          icon: const Icon(Icons.copy), // icon显示复制图标
          label: const Text('复制'), // label显示复制文本
        ),
        ElevatedButton.icon(
          // ElevatedButton.icon组件创建分享按钮
          onPressed: _shareText, // onPressed设置分享回调
          icon: const Icon(Icons.share), // icon显示分享图标
          label: const Text('分享'), // label显示分享文本
        ),
      ],
    );
  }

  /// 构建加载内容
  Widget _buildLoadingContent(ThemeData theme) {
    // _buildLoadingContent()方法构建加载状态UI
    return Center(
      // Center组件居中显示内容
      child: Column(
        // Column组件创建垂直布局
        mainAxisAlignment:
            MainAxisAlignment.center, // mainAxisAlignment设置主轴对齐方式为居中
        children: [
          // children列表包含加载指示器和文本
          CircularProgressIndicator(
            // CircularProgressIndicator组件显示圆形进度指示器
            color: theme.colorScheme.primary, // color设置指示器颜色
          ),
          const SizedBox(height: UIConstants.spacingM), // SizedBox组件创建垂直间距
          Text(
            // Text组件显示加载文本
            '正在识别文字...', // 显示的加载提示文本
            style: theme.textTheme.bodyMedium?.copyWith(
              // style设置文本样式
              color: theme.colorScheme.onSurface.withOpacity(
                0.7,
              ), // color设置文本颜色，withOpacity()设置透明度
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误内容
  Widget _buildErrorContent(ThemeData theme, Object error) {
    // _buildErrorContent()方法构建错误状态UI，error参数是错误对象
    return Center(
      // Center组件居中显示错误内容
      child: Padding(
        // Padding组件添加内边距
        padding: const EdgeInsets.all(UIConstants.spacingL), // padding设置大内边距
        child: Column(
          // Column组件创建垂直布局
          mainAxisAlignment:
              MainAxisAlignment.center, // mainAxisAlignment设置主轴对齐方式为居中
          children: [
            // children列表包含错误图标、文本和重试按钮
            Icon(
              // Icon组件显示错误图标
              Icons.error_outline, // Icons.error_outline错误轮廓图标
              size: UIConstants.iconSizeXL, // size设置图标大小为超大
              color: theme.colorScheme.error, // color设置图标颜色为错误色
            ),
            const SizedBox(height: UIConstants.spacingM), // SizedBox组件创建垂直间距
            Text(
              // Text组件显示错误标题
              'OCR识别失败', // 错误标题文本
              style: theme.textTheme.titleMedium?.copyWith(
                // style设置文本样式
                color: theme.colorScheme.error, // color设置文本颜色为错误色
                fontWeight: FontWeight.w600, // fontWeight设置字体粗细
              ),
            ),
            const SizedBox(height: UIConstants.spacingS), // SizedBox组件创建小间距
            Text(
              // Text组件显示错误详情
              error.toString(), // error.toString()将错误对象转换为字符串
              style: theme.textTheme.bodyMedium?.copyWith(
                // style设置文本样式
                color: theme.colorScheme.onSurface.withOpacity(
                  0.7,
                ), // color设置文本颜色
              ),
              textAlign: TextAlign.center, // textAlign设置文本对齐方式为居中
            ),
            const SizedBox(height: UIConstants.spacingL), // SizedBox组件创建大间距
            ElevatedButton.icon(
              // ElevatedButton.icon组件创建重试按钮
              onPressed: () => ref.refresh(
                ocrProcessProvider(
                  OCRProcessParams(
                    // onPressed设置重试回调，ref.refresh()刷新状态
                    imageData: widget.imageData, // imageData使用组件的图像数据
                    language: widget.language, // language使用组件的语言设置
                  ),
                ),
              ),
              icon: const Icon(Icons.refresh), // icon显示刷新图标
              label: const Text('重试'), // label显示重试文本
            ),
          ],
        ),
      ),
    );
  }

  /// 切换编辑状态
  void _toggleEditing() {
    // _toggleEditing()方法切换编辑状态
    setState(() {
      // setState()方法更新组件状态
      _isEditing = !_isEditing; // !操作符取反编辑状态
    });
  }

  /// 复制到剪贴板
  void _copyToClipboard() async {
    // _copyToClipboard()异步方法复制文本到剪贴板
    await Clipboard.setData(
      ClipboardData(text: _textController.text),
    ); // Clipboard.setData()设置剪贴板数据，ClipboardData包装文本数据

    if (mounted) {
      // mounted检查组件是否仍然挂载
      ScaffoldMessenger.of(context).showSnackBar(
        // ScaffoldMessenger.of(context).showSnackBar()显示提示消息
        const SnackBar(
          // SnackBar组件创建底部提示条
          content: Text('文本已复制到剪贴板'), // content设置提示内容
          duration: Duration(seconds: 2), // duration设置显示时长
        ),
      );
    }
  }

  /// 分享文本
  void _shareText() {
    // _shareText()方法分享文本内容
    // 这里可以集成分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      // 显示分享功能开发中的提示
      const SnackBar(
        // SnackBar组件创建提示条
        content: Text('分享功能开发中...'), // content设置提示内容
        duration: Duration(seconds: 2), // duration设置显示时长
      ),
    );
  }
}
