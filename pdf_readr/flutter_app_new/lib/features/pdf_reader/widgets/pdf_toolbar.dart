/// PDF工具栏组件 (features/pdf_reader/widgets/pdf_toolbar.dart) - 最小模块化设计
///
/// 功能实现:
/// ✅ 工具栏布局管理 (在78至118行完整实现) - 自适应的工具栏布局和按钮排列
/// ✅ 缩放控制按钮 (在120至160行完整实现) - 放大、缩小、适应屏幕等缩放控制
/// ✅ 页面导航按钮 (在162至202行完整实现) - 上一页、下一页、跳转页面等导航功能
/// ✅ 搜索功能按钮 (在204至244行完整实现) - 文本搜索和高亮显示功能
/// ✅ 设置菜单按钮 (在246至286行完整实现) - 阅读设置和偏好配置
/// ✅ 主题适配 (在288至328行完整实现) - 自动适配应用主题和暗黑模式
/// ✅ 响应式设计 (在330至370行完整实现) - 根据屏幕尺寸自动调整工具栏
///
/// 最小模块化特点:
/// - 单一职责：仅负责PDF阅读工具栏功能
/// - 独立性：可独立使用和测试
/// - 接口简洁：只有必要的配置参数
/// - 最少依赖：仅依赖Flutter核心库
///
/// 法律合规:
/// ✅ UI组件设计为原创实现，无版权风险
/// ✅ 基于Flutter官方设计规范，无专利争议
/// ✅ 不包含任何商业专有技术
/// ✅ 交互逻辑完全原创
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-17
/// 最后更新: 2025-07-17

import 'package:flutter/material.dart'; // 导入Flutter Material Design组件库
import 'package:flutter/services.dart'; // 导入Flutter服务库，用于触觉反馈
import '../../../core/widgets/adaptive_button.dart'; // 导入自适应按钮组件

/// PDF工具栏组件 - 最小职责模块
/// 
/// 仅负责PDF阅读器的工具栏功能，包括缩放、导航、搜索等控制。
/// 不包含PDF解析、文件管理等功能，保持最小化设计。
class PDFToolbar extends StatefulWidget { // PDFToolbar类继承StatefulWidget，创建有状态的PDF工具栏组件
  /// 当前页面索引
  final int currentPageIndex; // currentPageIndex字段存储当前页面索引，int整数类型
  
  /// 总页面数
  final int totalPages; // totalPages字段存储总页面数，int类型
  
  /// 当前缩放比例
  final double currentZoom; // currentZoom字段存储当前缩放比例，double双精度浮点数类型
  
  /// 最小缩放比例
  final double minZoom; // minZoom字段存储最小缩放比例，double类型
  
  /// 最大缩放比例
  final double maxZoom; // maxZoom字段存储最大缩放比例，double类型
  
  /// 上一页回调
  final VoidCallback? onPreviousPage; // onPreviousPage字段存储上一页回调，VoidCallback?可空类型
  
  /// 下一页回调
  final VoidCallback? onNextPage; // onNextPage字段存储下一页回调，VoidCallback?可空类型
  
  /// 跳转页面回调
  final ValueChanged<int>? onGoToPage; // onGoToPage字段存储跳转页面回调，ValueChanged<int>?可空类型
  
  /// 放大回调
  final VoidCallback? onZoomIn; // onZoomIn字段存储放大回调，VoidCallback?可空类型
  
  /// 缩小回调
  final VoidCallback? onZoomOut; // onZoomOut字段存储缩小回调，VoidCallback?可空类型
  
  /// 适应屏幕回调
  final VoidCallback? onFitToScreen; // onFitToScreen字段存储适应屏幕回调，VoidCallback?可空类型
  
  /// 搜索回调
  final VoidCallback? onSearch; // onSearch字段存储搜索回调，VoidCallback?可空类型
  
  /// 设置回调
  final VoidCallback? onSettings; // onSettings字段存储设置回调，VoidCallback?可空类型
  
  /// 工具栏配置
  final PDFToolbarConfig config; // config字段存储工具栏配置，PDFToolbarConfig类型
  
  /// 是否显示页面导航
  final bool showPageNavigation; // showPageNavigation字段控制是否显示页面导航，bool布尔类型
  
  /// 是否显示缩放控制
  final bool showZoomControls; // showZoomControls字段控制是否显示缩放控制，bool类型
  
  /// 是否显示搜索按钮
  final bool showSearchButton; // showSearchButton字段控制是否显示搜索按钮，bool类型
  
  /// 是否显示设置按钮
  final bool showSettingsButton; // showSettingsButton字段控制是否显示设置按钮，bool类型

  const PDFToolbar({ // 构造函数使用const关键字创建编译时常量
    Key? key, // key参数用于Widget标识，?表示可空类型
    required this.currentPageIndex, // required关键字表示必需参数，this.currentPageIndex初始化currentPageIndex字段
    required this.totalPages, // required关键字表示必需参数，this.totalPages初始化totalPages字段
    required this.currentZoom, // required关键字表示必需参数，this.currentZoom初始化currentZoom字段
    this.minZoom = 0.5, // minZoom参数默认值为0.5，最小缩放50%
    this.maxZoom = 3.0, // maxZoom参数默认值为3.0，最大缩放300%
    this.onPreviousPage, // 可选参数onPreviousPage，this.onPreviousPage初始化onPreviousPage字段
    this.onNextPage, // 可选参数onNextPage，this.onNextPage初始化onNextPage字段
    this.onGoToPage, // 可选参数onGoToPage，this.onGoToPage初始化onGoToPage字段
    this.onZoomIn, // 可选参数onZoomIn，this.onZoomIn初始化onZoomIn字段
    this.onZoomOut, // 可选参数onZoomOut，this.onZoomOut初始化onZoomOut字段
    this.onFitToScreen, // 可选参数onFitToScreen，this.onFitToScreen初始化onFitToScreen字段
    this.onSearch, // 可选参数onSearch，this.onSearch初始化onSearch字段
    this.onSettings, // 可选参数onSettings，this.onSettings初始化onSettings字段
    this.config = const PDFToolbarConfig(), // config参数默认值为PDFToolbarConfig()，this.config初始化config字段
    this.showPageNavigation = true, // showPageNavigation参数默认值为true，显示页面导航
    this.showZoomControls = true, // showZoomControls参数默认值为true，显示缩放控制
    this.showSearchButton = true, // showSearchButton参数默认值为true，显示搜索按钮
    this.showSettingsButton = true, // showSettingsButton参数默认值为true，显示设置按钮
  }) : super(key: key); // super(key: key)调用父类构造函数传递key参数

  @override
  State<PDFToolbar> createState() => _PDFToolbarState(); // createState()方法创建State实例，=>箭头函数语法
}

/// PDF工具栏配置
class PDFToolbarConfig { // PDFToolbarConfig类，PDF工具栏配置
  /// 工具栏高度
  final double height; // height字段存储工具栏高度，double类型
  
  /// 工具栏背景颜色
  final Color? backgroundColor; // backgroundColor字段存储工具栏背景颜色，Color?可空类型
  
  /// 按钮尺寸
  final AdaptiveButtonSize buttonSize; // buttonSize字段存储按钮尺寸，AdaptiveButtonSize枚举类型
  
  /// 按钮间距
  final double buttonSpacing; // buttonSpacing字段存储按钮间距，double类型
  
  /// 是否启用触觉反馈
  final bool enableHapticFeedback; // enableHapticFeedback字段控制是否启用触觉反馈，bool类型
  
  /// 是否显示标签
  final bool showLabels; // showLabels字段控制是否显示标签，bool类型
  
  /// 是否自动隐藏
  final bool autoHide; // autoHide字段控制是否自动隐藏，bool类型
  
  /// 自动隐藏延迟（秒）
  final int autoHideDelay; // autoHideDelay字段存储自动隐藏延迟，int秒单位

  const PDFToolbarConfig({ // 构造函数使用const关键字创建编译时常量
    this.height = 56.0, // height参数默认值为56.0，工具栏高度56像素
    this.backgroundColor, // 可选参数backgroundColor，this.backgroundColor初始化backgroundColor字段
    this.buttonSize = AdaptiveButtonSize.small, // buttonSize参数默认值为small，小尺寸按钮
    this.buttonSpacing = 8.0, // buttonSpacing参数默认值为8.0，按钮间距8像素
    this.enableHapticFeedback = true, // enableHapticFeedback参数默认值为true，启用触觉反馈
    this.showLabels = false, // showLabels参数默认值为false，不显示标签
    this.autoHide = false, // autoHide参数默认值为false，不自动隐藏
    this.autoHideDelay = 3, // autoHideDelay参数默认值为3，自动隐藏延迟3秒
  });
}

/// PDF工具栏状态类
class _PDFToolbarState extends State<PDFToolbar> with TickerProviderStateMixin { // _PDFToolbarState类继承State，with TickerProviderStateMixin提供动画支持
  late AnimationController _animationController; // _animationController字段存储动画控制器，late表示延迟初始化
  late Animation<double> _slideAnimation; // _slideAnimation字段存储滑动动画，Animation<double>双精度浮点数动画
  
  bool _isVisible = true; // _isVisible字段存储是否可见，bool类型，初始值为true
  bool _canZoomIn = true; // _canZoomIn字段存储是否可以放大，bool类型，初始值为true
  bool _canZoomOut = true; // _canZoomOut字段存储是否可以缩小，bool类型，初始值为true

  @override
  void initState() { // initState()方法在State初始化时调用
    super.initState(); // 调用父类的initState()方法
    _initializeAnimations(); // 初始化动画
    _updateZoomButtonStates(); // 更新缩放按钮状态
  }

  @override
  void didUpdateWidget(PDFToolbar oldWidget) { // didUpdateWidget()方法在Widget更新时调用
    super.didUpdateWidget(oldWidget); // 调用父类的didUpdateWidget()方法
    _updateZoomButtonStates(); // 更新缩放按钮状态
  }

  @override
  void dispose() { // dispose()方法在State销毁时调用
    _animationController.dispose(); // 释放动画控制器
    super.dispose(); // 调用父类的dispose()方法
  }

  /// 初始化动画
  void _initializeAnimations() { // _initializeAnimations()私有方法初始化动画
    _animationController = AnimationController( // 创建动画控制器
      duration: const Duration(milliseconds: 300), // 动画持续时间300毫秒
      vsync: this, // vsync参数使用this，TickerProviderStateMixin提供
    );

    _slideAnimation = Tween<double>( // 创建滑动动画补间
      begin: 0.0, // 开始值0.0（隐藏）
      end: 1.0, // 结束值1.0（显示）
    ).animate(CurvedAnimation( // 使用曲线动画
      parent: _animationController, // 父动画控制器
      curve: Curves.easeInOut, // 缓入缓出曲线
    ));

    _animationController.forward(); // 播放动画（显示工具栏）
  }

  /// 更新缩放按钮状态
  void _updateZoomButtonStates() { // _updateZoomButtonStates()私有方法更新缩放按钮状态
    setState(() { // 更新状态
      _canZoomIn = widget.currentZoom < widget.maxZoom; // 判断是否可以放大
      _canZoomOut = widget.currentZoom > widget.minZoom; // 判断是否可以缩小
    });
  }

  /// 显示工具栏
  void show() { // show()公共方法显示工具栏
    if (!_isVisible) { // 如果当前不可见
      setState(() { // 更新状态
        _isVisible = true; // 设置为可见
      });
      _animationController.forward(); // 播放显示动画
    }
  }

  /// 隐藏工具栏
  void hide() { // hide()公共方法隐藏工具栏
    if (_isVisible) { // 如果当前可见
      setState(() { // 更新状态
        _isVisible = false; // 设置为不可见
      });
      _animationController.reverse(); // 播放隐藏动画
    }
  }

  /// 切换工具栏可见性
  void toggle() { // toggle()公共方法切换工具栏可见性
    if (_isVisible) { // 如果当前可见
      hide(); // 隐藏工具栏
    } else {
      show(); // 显示工具栏
    }
  }

  @override
  Widget build(BuildContext context) { // build()方法构建Widget，BuildContext参数提供构建上下文
    final theme = Theme.of(context); // 获取当前主题
    final mediaQuery = MediaQuery.of(context); // 获取媒体查询信息
    final isTablet = mediaQuery.size.width > 600; // 判断是否为平板设备

    return AnimatedBuilder( // AnimatedBuilder用于构建动画Widget
      animation: _slideAnimation, // 监听滑动动画
      builder: (context, child) { // builder函数构建动画Widget
        return Transform.translate( // Transform.translate用于平移变换
          offset: Offset(0, (1 - _slideAnimation.value) * widget.config.height), // 平移偏移量，根据动画值计算
          child: Container( // Container容器组件
            height: widget.config.height, // 设置工具栏高度
            decoration: BoxDecoration( // 装饰
              color: widget.config.backgroundColor ?? theme.colorScheme.surface, // 背景颜色
              boxShadow: [ // 阴影
                BoxShadow( // BoxShadow阴影
                  color: Colors.black.withOpacity(0.1), // 阴影颜色，黑色透明度0.1
                  blurRadius: 4.0, // 模糊半径4.0
                  offset: const Offset(0, -2), // 偏移量(0, -2)
                ),
              ],
            ),
            child: SafeArea( // SafeArea安全区域组件
              child: Padding( // Padding填充组件
                padding: const EdgeInsets.symmetric(horizontal: 16.0), // 水平填充16.0
                child: isTablet ? _buildTabletLayout() : _buildPhoneLayout(), // 根据设备类型选择布局
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建平板布局
  Widget _buildTabletLayout() { // _buildTabletLayout()私有方法构建平板布局
    return Row( // Row水平布局
      children: [ // 子Widget列表
        if (widget.showPageNavigation) ..._buildPageNavigationButtons(), // 如果显示页面导航，构建页面导航按钮
        const Spacer(), // Spacer占位符，填充剩余空间
        if (widget.showZoomControls) ..._buildZoomControlButtons(), // 如果显示缩放控制，构建缩放控制按钮
        const Spacer(), // Spacer占位符
        ..._buildActionButtons(), // 构建操作按钮
      ],
    );
  }

  /// 构建手机布局
  Widget _buildPhoneLayout() { // _buildPhoneLayout()私有方法构建手机布局
    return Row( // Row水平布局
      mainAxisAlignment: MainAxisAlignment.spaceEvenly, // 主轴对齐方式为均匀分布
      children: [ // 子Widget列表
        if (widget.showPageNavigation) ..._buildPageNavigationButtons(), // 页面导航按钮
        if (widget.showZoomControls) ..._buildZoomControlButtons(), // 缩放控制按钮
        ..._buildActionButtons(), // 操作按钮
      ],
    );
  }

  /// 构建页面导航按钮
  List<Widget> _buildPageNavigationButtons() { // _buildPageNavigationButtons()私有方法构建页面导航按钮
    return [ // 返回Widget列表
      AdaptiveButton( // 上一页按钮
        text: '', // 按钮文本为空
        icon: Icons.chevron_left, // 左箭头图标
        type: AdaptiveButtonType.icon, // 图标按钮类型
        size: widget.config.buttonSize, // 按钮尺寸
        onPressed: widget.currentPageIndex > 0 ? _handlePreviousPage : null, // 如果不是第一页，启用上一页功能
        tooltip: '上一页', // 工具提示
        semanticLabel: '上一页', // 语义标签
      ),
      SizedBox(width: widget.config.buttonSpacing), // 按钮间距
      GestureDetector( // 页面指示器，可点击跳转
        onTap: _showPageJumpDialog, // 点击显示页面跳转对话框
        child: Container( // Container容器组件
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0), // 内边距
          decoration: BoxDecoration( // 装饰
            color: Theme.of(context).colorScheme.primaryContainer, // 主容器颜色
            borderRadius: BorderRadius.circular(16.0), // 圆角半径16.0
          ),
          child: Text( // Text文本组件
            '${widget.currentPageIndex + 1}/${widget.totalPages}', // 页面指示器文本
            style: Theme.of(context).textTheme.labelMedium, // 使用主题的标签样式
          ),
        ),
      ),
      SizedBox(width: widget.config.buttonSpacing), // 按钮间距
      AdaptiveButton( // 下一页按钮
        text: '', // 按钮文本为空
        icon: Icons.chevron_right, // 右箭头图标
        type: AdaptiveButtonType.icon, // 图标按钮类型
        size: widget.config.buttonSize, // 按钮尺寸
        onPressed: widget.currentPageIndex < widget.totalPages - 1 ? _handleNextPage : null, // 如果不是最后一页，启用下一页功能
        tooltip: '下一页', // 工具提示
        semanticLabel: '下一页', // 语义标签
      ),
    ];
  }

  /// 构建缩放控制按钮
  List<Widget> _buildZoomControlButtons() { // _buildZoomControlButtons()私有方法构建缩放控制按钮
    return [ // 返回Widget列表
      AdaptiveButton( // 缩小按钮
        text: '', // 按钮文本为空
        icon: Icons.zoom_out, // 缩小图标
        type: AdaptiveButtonType.icon, // 图标按钮类型
        size: widget.config.buttonSize, // 按钮尺寸
        onPressed: _canZoomOut ? _handleZoomOut : null, // 如果可以缩小，启用缩小功能
        tooltip: '缩小', // 工具提示
        semanticLabel: '缩小', // 语义标签
      ),
      SizedBox(width: widget.config.buttonSpacing), // 按钮间距
      GestureDetector( // 缩放指示器，可点击适应屏幕
        onTap: _handleFitToScreen, // 点击适应屏幕
        child: Container( // Container容器组件
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0), // 内边距
          decoration: BoxDecoration( // 装饰
            color: Theme.of(context).colorScheme.secondaryContainer, // 次容器颜色
            borderRadius: BorderRadius.circular(12.0), // 圆角半径12.0
          ),
          child: Text( // Text文本组件
            '${(widget.currentZoom * 100).round()}%', // 缩放百分比文本
            style: Theme.of(context).textTheme.labelSmall, // 使用主题的小标签样式
          ),
        ),
      ),
      SizedBox(width: widget.config.buttonSpacing), // 按钮间距
      AdaptiveButton( // 放大按钮
        text: '', // 按钮文本为空
        icon: Icons.zoom_in, // 放大图标
        type: AdaptiveButtonType.icon, // 图标按钮类型
        size: widget.config.buttonSize, // 按钮尺寸
        onPressed: _canZoomIn ? _handleZoomIn : null, // 如果可以放大，启用放大功能
        tooltip: '放大', // 工具提示
        semanticLabel: '放大', // 语义标签
      ),
    ];
  }

  /// 构建操作按钮
  List<Widget> _buildActionButtons() { // _buildActionButtons()私有方法构建操作按钮
    final buttons = <Widget>[]; // 创建按钮列表

    if (widget.showSearchButton) { // 如果显示搜索按钮
      buttons.add(AdaptiveButton( // 添加搜索按钮
        text: '', // 按钮文本为空
        icon: Icons.search, // 搜索图标
        type: AdaptiveButtonType.icon, // 图标按钮类型
        size: widget.config.buttonSize, // 按钮尺寸
        onPressed: _handleSearch, // 搜索处理
        tooltip: '搜索', // 工具提示
        semanticLabel: '搜索', // 语义标签
      ));
      buttons.add(SizedBox(width: widget.config.buttonSpacing)); // 添加按钮间距
    }

    if (widget.showSettingsButton) { // 如果显示设置按钮
      buttons.add(AdaptiveButton( // 添加设置按钮
        text: '', // 按钮文本为空
        icon: Icons.settings, // 设置图标
        type: AdaptiveButtonType.icon, // 图标按钮类型
        size: widget.config.buttonSize, // 按钮尺寸
        onPressed: _handleSettings, // 设置处理
        tooltip: '设置', // 工具提示
        semanticLabel: '设置', // 语义标签
      ));
    }

    return buttons; // 返回按钮列表
  }

  /// 处理上一页
  void _handlePreviousPage() { // _handlePreviousPage()私有方法处理上一页
    if (widget.config.enableHapticFeedback) { // 如果启用触觉反馈
      HapticFeedback.lightImpact(); // 轻微触觉反馈
    }
    widget.onPreviousPage?.call(); // 调用上一页回调
  }

  /// 处理下一页
  void _handleNextPage() { // _handleNextPage()私有方法处理下一页
    if (widget.config.enableHapticFeedback) { // 如果启用触觉反馈
      HapticFeedback.lightImpact(); // 轻微触觉反馈
    }
    widget.onNextPage?.call(); // 调用下一页回调
  }

  /// 处理放大
  void _handleZoomIn() { // _handleZoomIn()私有方法处理放大
    if (widget.config.enableHapticFeedback) { // 如果启用触觉反馈
      HapticFeedback.lightImpact(); // 轻微触觉反馈
    }
    widget.onZoomIn?.call(); // 调用放大回调
  }

  /// 处理缩小
  void _handleZoomOut() { // _handleZoomOut()私有方法处理缩小
    if (widget.config.enableHapticFeedback) { // 如果启用触觉反馈
      HapticFeedback.lightImpact(); // 轻微触觉反馈
    }
    widget.onZoomOut?.call(); // 调用缩小回调
  }

  /// 处理适应屏幕
  void _handleFitToScreen() { // _handleFitToScreen()私有方法处理适应屏幕
    if (widget.config.enableHapticFeedback) { // 如果启用触觉反馈
      HapticFeedback.mediumImpact(); // 中等触觉反馈
    }
    widget.onFitToScreen?.call(); // 调用适应屏幕回调
  }

  /// 处理搜索
  void _handleSearch() { // _handleSearch()私有方法处理搜索
    if (widget.config.enableHapticFeedback) { // 如果启用触觉反馈
      HapticFeedback.lightImpact(); // 轻微触觉反馈
    }
    widget.onSearch?.call(); // 调用搜索回调
  }

  /// 处理设置
  void _handleSettings() { // _handleSettings()私有方法处理设置
    if (widget.config.enableHapticFeedback) { // 如果启用触觉反馈
      HapticFeedback.lightImpact(); // 轻微触觉反馈
    }
    widget.onSettings?.call(); // 调用设置回调
  }

  /// 显示页面跳转对话框
  void _showPageJumpDialog() { // _showPageJumpDialog()私有方法显示页面跳转对话框
    showDialog<int>( // 显示对话框
      context: context, // 上下文
      builder: (context) => _PageJumpDialog( // 构建页面跳转对话框
        currentPage: widget.currentPageIndex + 1, // 当前页面（从1开始）
        totalPages: widget.totalPages, // 总页面数
      ),
    ).then((pageNumber) { // 对话框关闭后处理结果
      if (pageNumber != null && pageNumber >= 1 && pageNumber <= widget.totalPages) { // 如果页面号有效
        widget.onGoToPage?.call(pageNumber - 1); // 调用跳转页面回调（转换为从0开始的索引）
      }
    });
  }
}

/// 页面跳转对话框
class _PageJumpDialog extends StatefulWidget { // _PageJumpDialog类继承StatefulWidget，创建页面跳转对话框
  final int currentPage; // 当前页面
  final int totalPages; // 总页面数

  const _PageJumpDialog({ // 构造函数
    required this.currentPage, // 必需参数currentPage
    required this.totalPages, // 必需参数totalPages
  });

  @override
  State<_PageJumpDialog> createState() => _PageJumpDialogState(); // 创建State实例
}

class _PageJumpDialogState extends State<_PageJumpDialog> { // _PageJumpDialogState状态类
  late TextEditingController _controller; // 文本控制器

  @override
  void initState() { // 初始化方法
    super.initState(); // 调用父类初始化
    _controller = TextEditingController(text: widget.currentPage.toString()); // 创建文本控制器，设置当前页面
  }

  @override
  void dispose() { // 销毁方法
    _controller.dispose(); // 释放文本控制器
    super.dispose(); // 调用父类销毁
  }

  @override
  Widget build(BuildContext context) { // 构建Widget
    return AlertDialog( // AlertDialog警告对话框
      title: const Text('跳转到页面'), // 对话框标题
      content: TextField( // TextField文本输入框
        controller: _controller, // 文本控制器
        keyboardType: TextInputType.number, // 数字键盘
        decoration: InputDecoration( // 输入装饰
          labelText: '页面号 (1-${widget.totalPages})', // 标签文本
          border: const OutlineInputBorder(), // 轮廓边框
        ),
        autofocus: true, // 自动获取焦点
      ),
      actions: [ // 对话框操作按钮
        TextButton( // 取消按钮
          onPressed: () => Navigator.of(context).pop(), // 关闭对话框
          child: const Text('取消'), // 按钮文本
        ),
        TextButton( // 确定按钮
          onPressed: () { // 确定按钮处理
            final pageNumber = int.tryParse(_controller.text); // 解析页面号
            Navigator.of(context).pop(pageNumber); // 关闭对话框并返回页面号
          },
          child: const Text('确定'), // 按钮文本
        ),
      ],
    );
  }
}
