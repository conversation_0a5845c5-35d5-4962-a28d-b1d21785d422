/// 应用程序主题配置 (core/theme/app_theme.dart)
///
/// 功能实现:
/// ✅ Material Design 3主题 (完整实现)
/// ✅ 深色和浅色主题 (完整实现)
/// ✅ 自定义颜色方案 (完整实现)
/// ✅ 文字样式定义 (完整实现)
/// ✅ 移动端优化配置 (完整实现)
///
/// 设计原则:
/// - 遵循Material Design 3规范
/// - 支持深色和浅色主题
/// - 移动端阅读优化
/// - 无障碍访问支持
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-15

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../constants/app_constants.dart';

/// 应用程序主题配置类
class AppTheme {
  // 私有构造函数，防止实例化
  AppTheme._();

  /// 主要颜色定义
  static const Color primaryColor = Color(0xFF1976D2); // 蓝色主色调
  static const Color primaryVariantColor = Color(0xFF1565C0); // 深蓝色变体
  static const Color secondaryColor = Color(0xFF03DAC6); // 青色辅助色
  static const Color secondaryVariantColor = Color(0xFF018786); // 深青色变体

  /// 功能性颜色
  static const Color errorColor = Color(0xFFB00020); // 错误红色
  static const Color warningColor = Color(0xFFFF9800); // 警告橙色
  static const Color successColor = Color(0xFF4CAF50); // 成功绿色
  static const Color infoColor = Color(0xFF2196F3); // 信息蓝色

  /// 中性颜色
  static const Color surfaceColor = Color(0xFFFFFBFE); // 表面颜色
  static const Color backgroundLight = Color(0xFFFFFBFE); // 浅色背景
  static const Color backgroundDark = Color(0xFF121212); // 深色背景

  /// 文本颜色
  static const Color textPrimaryLight = Color(0xFF1C1B1F); // 浅色主题主要文本
  static const Color textSecondaryLight = Color(0xFF49454F); // 浅色主题次要文本
  static const Color textPrimaryDark = Color(0xFFE6E1E5); // 深色主题主要文本
  static const Color textSecondaryDark = Color(0xFFCAC4D0); // 深色主题次要文本

  /// 获取浅色主题
  static ThemeData get lightTheme {
    final ColorScheme colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: Brightness.light,
      primary: primaryColor,
      secondary: secondaryColor,
      error: errorColor,
      surface: surfaceColor,
      // background: backgroundLight, // 已弃用，使用surface替代
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      brightness: Brightness.light,

      /// AppBar主题
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: TextStyle(
          fontSize: UIConstants.fontSizeXL,
          fontWeight: FontWeight.w600,
          color: colorScheme.onSurface,
        ),
        iconTheme: IconThemeData(
          color: colorScheme.onSurface,
          size: UIConstants.iconSizeM,
        ),
      ),

      /// 卡片主题
      cardTheme: CardThemeData(
        elevation: UIConstants.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.cardRadius),
        ),
        margin: const EdgeInsets.all(UIConstants.spacingS),
      ),

      /// 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: UIConstants.elevationM,
          padding: const EdgeInsets.symmetric(
            horizontal: UIConstants.spacingL,
            vertical: UIConstants.spacingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
          ),
          textStyle: const TextStyle(
            fontSize: UIConstants.fontSizeL,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      /// 文本按钮主题
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: UIConstants.spacingL,
            vertical: UIConstants.spacingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
          ),
          textStyle: const TextStyle(
            fontSize: UIConstants.fontSizeL,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      /// 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusM),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusM),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusM),
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: UIConstants.spacingM,
          vertical: UIConstants.spacingM,
        ),
      ),

      /// 列表瓦片主题
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: UIConstants.spacingM,
          vertical: UIConstants.spacingS,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusM),
        ),
      ),

      /// 分割线主题
      dividerTheme: DividerThemeData(
        thickness: UIConstants.dividerThickness,
        indent: UIConstants.dividerIndent,
        endIndent: UIConstants.dividerIndent,
        color: colorScheme.outline.withValues(alpha: 0.2),
      ),

      /// 图标主题
      iconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: UIConstants.iconSizeM,
      ),

      /// 文本主题
      textTheme: _buildTextTheme(colorScheme),

      /// 滚动条主题
      scrollbarTheme: ScrollbarThemeData(
        thumbColor: WidgetStateProperty.all(
          colorScheme.outline.withValues(alpha: 0.3),
        ),
        trackColor: WidgetStateProperty.all(
          colorScheme.outline.withValues(alpha: 0.1),
        ),
        radius: const Radius.circular(UIConstants.radiusS),
        thickness: WidgetStateProperty.all(4.0),
      ),

      /// 底部导航栏主题
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurface.withValues(alpha: 0.6),
        type: BottomNavigationBarType.fixed,
        elevation: UIConstants.elevationL,
      ),

      /// 浮动操作按钮主题
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: UIConstants.elevationL,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusXL),
        ),
      ),

      /// 对话框主题
      dialogTheme: DialogThemeData(
        backgroundColor: colorScheme.surface,
        elevation: UIConstants.elevationXL,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusXL),
        ),
        titleTextStyle: TextStyle(
          fontSize: UIConstants.fontSizeXL,
          fontWeight: FontWeight.w600,
          color: colorScheme.onSurface,
        ),
        contentTextStyle: TextStyle(
          fontSize: UIConstants.fontSizeM,
          color: colorScheme.onSurface,
        ),
      ),

      /// 底部表单主题
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: colorScheme.surface,
        elevation: UIConstants.elevationXL,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(UIConstants.radiusXL),
          ),
        ),
      ),

      /// 芯片主题
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceVariant,
        selectedColor: colorScheme.primary,
        labelStyle: TextStyle(
          fontSize: UIConstants.fontSizeM,
          color: colorScheme.onSurfaceVariant,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusL),
        ),
      ),
    );
  }

  /// 获取深色主题
  static ThemeData get darkTheme {
    final ColorScheme colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: Brightness.dark,
      primary: primaryColor,
      secondary: secondaryColor,
      error: errorColor,
      surface: const Color(0xFF1C1B1F),
      // background: backgroundDark, // 已弃用，使用surface替代
    );

    return lightTheme.copyWith(
      colorScheme: colorScheme,
      brightness: Brightness.dark,

      /// AppBar主题 - 深色版本
      appBarTheme: lightTheme.appBarTheme.copyWith(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          fontSize: UIConstants.fontSizeXL,
          fontWeight: FontWeight.w600,
          color: colorScheme.onSurface,
        ),
        iconTheme: IconThemeData(
          color: colorScheme.onSurface,
          size: UIConstants.iconSizeM,
        ),
      ),

      /// 文本主题 - 深色版本
      textTheme: _buildTextTheme(colorScheme),

      /// 输入框主题 - 深色版本
      inputDecorationTheme: lightTheme.inputDecorationTheme.copyWith(
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      ),

      /// 底部导航栏主题 - 深色版本
      bottomNavigationBarTheme: lightTheme.bottomNavigationBarTheme.copyWith(
        backgroundColor: colorScheme.surface,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }

  /// 构建文本主题
  static TextTheme _buildTextTheme(ColorScheme colorScheme) {
    return TextTheme(
      /// 显示文本样式
      displayLarge: TextStyle(
        fontSize: 57.0,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.12,
      ),
      displayMedium: TextStyle(
        fontSize: 45.0,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.16,
      ),
      displaySmall: TextStyle(
        fontSize: 36.0,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.22,
      ),

      /// 标题文本样式
      headlineLarge: TextStyle(
        fontSize: 32.0,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
        height: 1.25,
      ),
      headlineMedium: TextStyle(
        fontSize: UIConstants.fontSizeHeading,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
        height: 1.29,
      ),
      headlineSmall: TextStyle(
        fontSize: UIConstants.fontSizeTitle,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
        height: 1.33,
      ),

      /// 标题文本样式
      titleLarge: TextStyle(
        fontSize: UIConstants.fontSizeXXL,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
        height: 1.27,
      ),
      titleMedium: TextStyle(
        fontSize: UIConstants.fontSizeXL,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.50,
      ),
      titleSmall: TextStyle(
        fontSize: UIConstants.fontSizeL,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.43,
      ),

      /// 正文文本样式
      bodyLarge: TextStyle(
        fontSize: UIConstants.fontSizeL,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.50,
      ),
      bodyMedium: TextStyle(
        fontSize: UIConstants.fontSizeM,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.43,
      ),
      bodySmall: TextStyle(
        fontSize: UIConstants.fontSizeS,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurfaceVariant,
        height: 1.33,
      ),

      /// 标签文本样式
      labelLarge: TextStyle(
        fontSize: UIConstants.fontSizeM,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.43,
      ),
      labelMedium: TextStyle(
        fontSize: UIConstants.fontSizeS,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.33,
      ),
      labelSmall: TextStyle(
        fontSize: UIConstants.fontSizeXS,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurfaceVariant,
        height: 1.45,
      ),
    );
  }

  /// 获取自定义颜色扩展
  static CustomColors getCustomColors(ColorScheme colorScheme) {
    return CustomColors(
      warning: warningColor,
      onWarning: Colors.white,
      success: successColor,
      onSuccess: Colors.white,
      info: infoColor,
      onInfo: Colors.white,
    );
  }
}

/// 自定义颜色扩展
class CustomColors {
  const CustomColors({
    required this.warning,
    required this.onWarning,
    required this.success,
    required this.onSuccess,
    required this.info,
    required this.onInfo,
  });

  final Color warning;
  final Color onWarning;
  final Color success;
  final Color onSuccess;
  final Color info;
  final Color onInfo;
}

/// 主题扩展，用于在BuildContext中获取自定义颜色
extension CustomColorsExtension on ThemeData {
  CustomColors get customColors => AppTheme.getCustomColors(colorScheme);
}
