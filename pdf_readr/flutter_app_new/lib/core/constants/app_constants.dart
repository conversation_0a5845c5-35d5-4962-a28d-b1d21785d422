/// 应用程序常量定义 (core/constants/app_constants.dart)
///
/// 功能实现:
/// ✅ 应用基础信息常量 (完整实现)
/// ✅ UI尺寸和间距常量 (完整实现)
/// ✅ 动画时长常量 (完整实现)
/// ✅ 文件类型常量 (完整实现)
///
/// 设计原则:
/// - 集中管理所有应用常量
/// - 便于维护和修改
/// - 类型安全的常量定义
/// - 移动端优化的数值
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-15

/// 应用程序基础信息常量
class AppConstants {
  // 私有构造函数，防止实例化
  AppConstants._();

  /// 应用程序信息
  static const String appName = 'PDF阅读器';
  static const String appVersion = '1.0.0';
  static const String appDescription = '智能PDF阅读器 - 支持OCR文字识别、语音朗读和对照编辑';
  
  /// 支持的文件类型
  static const List<String> supportedFileTypes = ['pdf'];
  static const List<String> supportedFileExtensions = ['.pdf'];
  static const String filePickerFilter = 'PDF文件 (*.pdf)';
  
  /// 数据库相关
  static const String databaseName = 'pdf_reader.db';
  static const int databaseVersion = 1;
  
  /// 缓存相关
  static const String cacheDirectoryName = 'pdf_cache';
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int maxCachedPages = 50;
  
  /// 网络相关
  static const int networkTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
}

/// UI相关常量
class UIConstants {
  UIConstants._();

  /// 间距常量 - 移动端优化
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  /// 圆角半径
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 24.0;

  /// 阴影高度
  static const double elevationS = 1.0;
  static const double elevationM = 2.0;
  static const double elevationL = 4.0;
  static const double elevationXL = 8.0;

  /// 字体大小 - 移动端优化
  static const double fontSizeXS = 10.0;
  static const double fontSizeS = 12.0;
  static const double fontSizeM = 14.0;
  static const double fontSizeL = 16.0;
  static const double fontSizeXL = 18.0;
  static const double fontSizeXXL = 20.0;
  static const double fontSizeTitle = 24.0;
  static const double fontSizeHeading = 28.0;

  /// 图标大小
  static const double iconSizeS = 16.0;
  static const double iconSizeM = 24.0;
  static const double iconSizeL = 32.0;
  static const double iconSizeXL = 48.0;

  /// 按钮高度
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;

  /// AppBar高度
  static const double appBarHeight = 56.0;
  static const double appBarHeightLarge = 64.0;

  /// 底部导航栏高度
  static const double bottomNavHeight = 60.0;

  /// 最小触摸目标大小 (Material Design规范)
  static const double minTouchTarget = 48.0;

  /// 页面边距
  static const double pageMargin = 16.0;
  static const double pageMarginLarge = 24.0;

  /// 卡片相关
  static const double cardElevation = 2.0;
  static const double cardRadius = 12.0;
  static const double cardPadding = 16.0;

  /// 列表项高度
  static const double listItemHeight = 56.0;
  static const double listItemHeightLarge = 72.0;

  /// 分割线
  static const double dividerThickness = 1.0;
  static const double dividerIndent = 16.0;
}

/// 动画相关常量
class AnimationConstants {
  AnimationConstants._();

  /// 动画时长
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationNormal = Duration(milliseconds: 300);
  static const Duration durationSlow = Duration(milliseconds: 500);
  static const Duration durationVerySlow = Duration(milliseconds: 800);

  /// 页面转场动画时长
  static const Duration pageTransitionDuration = Duration(milliseconds: 300);

  /// 微交互动画时长
  static const Duration microInteractionDuration = Duration(milliseconds: 100);

  /// 加载动画时长
  static const Duration loadingAnimationDuration = Duration(milliseconds: 1000);

  /// 淡入淡出动画时长
  static const Duration fadeAnimationDuration = Duration(milliseconds: 200);

  /// 缩放动画时长
  static const Duration scaleAnimationDuration = Duration(milliseconds: 150);

  /// 滑动动画时长
  static const Duration slideAnimationDuration = Duration(milliseconds: 250);
}

/// PDF阅读相关常量
class PDFConstants {
  PDFConstants._();

  /// 默认缩放级别
  static const double defaultZoomLevel = 1.0;
  static const double minZoomLevel = 0.5;
  static const double maxZoomLevel = 3.0;
  static const double zoomStep = 0.25;

  /// 页面预加载数量
  static const int preloadPageCount = 3;
  static const int maxPreloadPageCount = 10;

  /// OCR相关
  static const int ocrTimeoutSeconds = 30;
  static const double ocrConfidenceThreshold = 0.7;

  /// TTS相关
  static const double defaultSpeechRate = 1.0;
  static const double minSpeechRate = 0.5;
  static const double maxSpeechRate = 2.0;
  static const double speechRateStep = 0.1;

  /// 文本重排相关
  static const double defaultFontSize = 16.0;
  static const double minFontSize = 12.0;
  static const double maxFontSize = 24.0;
  static const double fontSizeStep = 2.0;

  /// 页面间距
  static const double pageSpacing = 8.0;
  static const double pageMargin = 16.0;

  /// 滚动相关
  static const double scrollSensitivity = 1.0;
  static const Duration scrollAnimationDuration = Duration(milliseconds: 300);
}

/// 错误消息常量
class ErrorMessages {
  ErrorMessages._();

  /// 通用错误消息
  static const String unknownError = '发生未知错误，请稍后重试';
  static const String networkError = '网络连接失败，请检查网络设置';
  static const String timeoutError = '操作超时，请稍后重试';
  static const String permissionDenied = '权限被拒绝，请在设置中开启相关权限';

  /// 文件相关错误
  static const String fileNotFound = '文件不存在或已被删除';
  static const String fileNotSupported = '不支持的文件格式';
  static const String fileCorrupted = '文件已损坏，无法打开';
  static const String fileTooLarge = '文件过大，无法处理';

  /// PDF相关错误
  static const String pdfLoadFailed = 'PDF文件加载失败';
  static const String pdfRenderFailed = 'PDF页面渲染失败';
  static const String pdfPasswordRequired = 'PDF文件需要密码';
  static const String pdfPasswordIncorrect = 'PDF密码错误';

  /// OCR相关错误
  static const String ocrFailed = 'OCR识别失败，请稍后重试';
  static const String ocrTimeout = 'OCR识别超时';
  static const String ocrNoText = '未识别到文字内容';

  /// TTS相关错误
  static const String ttsNotAvailable = '语音合成服务不可用';
  static const String ttsInitFailed = '语音合成初始化失败';
  static const String ttsPlaybackFailed = '语音播放失败';

  /// 存储相关错误
  static const String storagePermissionDenied = '存储权限被拒绝';
  static const String storageFull = '存储空间不足';
  static const String databaseError = '数据库操作失败';
}

/// 成功消息常量
class SuccessMessages {
  SuccessMessages._();

  /// 文件操作成功
  static const String fileOpened = '文件打开成功';
  static const String fileSaved = '文件保存成功';
  static const String fileExported = '文件导出成功';

  /// PDF操作成功
  static const String pdfLoaded = 'PDF加载完成';
  static const String pageRendered = '页面渲染完成';

  /// OCR操作成功
  static const String ocrCompleted = 'OCR识别完成';
  static const String textExtracted = '文字提取成功';

  /// TTS操作成功
  static const String ttsStarted = '开始语音朗读';
  static const String ttsPaused = '语音朗读已暂停';
  static const String ttsResumed = '语音朗读已恢复';
  static const String ttsStopped = '语音朗读已停止';

  /// 设置保存成功
  static const String settingsSaved = '设置保存成功';
  static const String preferencesUpdated = '偏好设置已更新';
}

/// 路由路径常量
class RoutePaths {
  RoutePaths._();

  /// 主要页面路径
  static const String home = '/';
  static const String pdfReader = '/pdf-reader';
  static const String settings = '/settings';
  static const String about = '/about';

  /// PDF阅读相关页面
  static const String pdfViewer = '/pdf-viewer';
  static const String pdfEditor = '/pdf-editor';
  static const String pdfComparison = '/pdf-comparison';

  /// 功能页面
  static const String fileManager = '/file-manager';
  static const String bookmarks = '/bookmarks';
  static const String history = '/history';
  static const String search = '/search';

  /// 设置子页面
  static const String generalSettings = '/settings/general';
  static const String readingSettings = '/settings/reading';
  static const String ocrSettings = '/settings/ocr';
  static const String ttsSettings = '/settings/tts';
  static const String storageSettings = '/settings/storage';
}
