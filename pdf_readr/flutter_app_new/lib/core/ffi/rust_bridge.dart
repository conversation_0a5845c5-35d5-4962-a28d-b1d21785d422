/// Rust FFI桥接 (core/ffi/rust_bridge.dart)
///
/// 功能实现:
/// ✅ Rust库动态加载 (完整实现)
/// ✅ C函数绑定定义 (完整实现)
/// ✅ 数据类型转换 (完整实现)
/// ✅ 错误处理封装 (完整实现)
/// ✅ 内存管理 (完整实现)
///
/// 设计原则:
/// - 类型安全的FFI调用
/// - 自动内存管理
/// - 异常安全的错误处理
/// - 平台兼容的库加载
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-15

import 'dart:ffi' as ffi;
import 'dart:io';
import 'dart:typed_data';

import 'package:ffi/ffi.dart';

/// FFI错误码枚举 - 与Rust端保持一致
enum FFIErrorCode {
  success(0),
  nullPointer(1),
  invalidParameter(2),
  fileNotFound(3),
  memoryAllocation(4),
  pdfParsingFailed(5),
  ocrProcessingFailed(6),
  databaseError(7),
  internalError(8),
  unknownError(99);

  const FFIErrorCode(this.value);
  final int value;

  static FFIErrorCode fromValue(int value) {
    return FFIErrorCode.values.firstWhere(
      (e) => e.value == value,
      orElse: () => FFIErrorCode.unknownError,
    );
  }
}

/// PDF文档信息结构体 - 与Rust端C结构体对应
final class FFIDocumentInfo extends ffi.Struct {
  @ffi.Int64()
  external int id;

  external ffi.Pointer<ffi.Char> title;

  external ffi.Pointer<ffi.Char> author;

  @ffi.Int32()
  external int pageCount;

  @ffi.Int64()
  external int fileSize;

  @ffi.Int32()
  external int isEncrypted;
}

/// OCR结果结构体 - 与Rust端C结构体对应
final class FFIOCRResult extends ffi.Struct {
  external ffi.Pointer<ffi.Char> text;

  @ffi.Float()
  external double confidence;

  @ffi.Int64()
  external int processingTimeMs;

  @ffi.Int32()
  external int characterCount;
}

/// PDF页面渲染结果结构体 - 与Rust端C结构体对应
final class FFIPageRenderResult extends ffi.Struct {
  external ffi.Pointer<ffi.Uint8> imageData;

  @ffi.Size()
  external int imageSize;

  @ffi.Int32()
  external int width;

  @ffi.Int32()
  external int height;

  @ffi.Int64()
  external int renderTimeMs;

  @ffi.Int32()
  external int format;
}

/// Dart端的文档信息类
class DocumentInfo {
  final int id;
  final String title;
  final String? author;
  final int pageCount;
  final int fileSize;
  final bool isEncrypted;

  const DocumentInfo({
    required this.id,
    required this.title,
    this.author,
    required this.pageCount,
    required this.fileSize,
    required this.isEncrypted,
  });

  factory DocumentInfo.fromFFI(FFIDocumentInfo ffiInfo) {
    return DocumentInfo(
      id: ffiInfo.id,
      title: ffiInfo.title.toDartString(),
      author: ffiInfo.author.address != 0
          ? ffiInfo.author.toDartString()
          : null,
      pageCount: ffiInfo.pageCount,
      fileSize: ffiInfo.fileSize,
      isEncrypted: ffiInfo.isEncrypted != 0,
    );
  }
}

/// Dart端的OCR结果类
class OCRResult {
  final String text;
  final double confidence;
  final int processingTimeMs;
  final int characterCount;

  const OCRResult({
    required this.text,
    required this.confidence,
    required this.processingTimeMs,
    required this.characterCount,
  });

  factory OCRResult.fromFFI(FFIOCRResult ffiResult) {
    return OCRResult(
      text: ffiResult.text.toDartString(),
      confidence: ffiResult.confidence,
      processingTimeMs: ffiResult.processingTimeMs,
      characterCount: ffiResult.characterCount,
    );
  }
}

/// Dart端的页面渲染结果类
class PageRenderResult {
  final Uint8List imageData;
  final int width;
  final int height;
  final int renderTimeMs;
  final ImageFormat format;

  const PageRenderResult({
    required this.imageData,
    required this.width,
    required this.height,
    required this.renderTimeMs,
    required this.format,
  });

  factory PageRenderResult.fromFFI(FFIPageRenderResult ffiResult) {
    // 复制图像数据到Dart管理的内存
    final imageData = Uint8List.fromList(
      ffiResult.imageData.asTypedList(ffiResult.imageSize),
    );

    return PageRenderResult(
      imageData: imageData,
      width: ffiResult.width,
      height: ffiResult.height,
      renderTimeMs: ffiResult.renderTimeMs,
      format: ImageFormat.fromValue(ffiResult.format),
    );
  }
}

/// 图像格式枚举
enum ImageFormat {
  rgba(0),
  rgb(1),
  gray(2);

  const ImageFormat(this.value);
  final int value;

  static ImageFormat fromValue(int value) {
    return ImageFormat.values.firstWhere(
      (e) => e.value == value,
      orElse: () => ImageFormat.rgba,
    );
  }
}

/// FFI函数签名定义
typedef FFIInitializeC = ffi.Int32 Function();
typedef FFICleanupC = ffi.Int32 Function();
typedef FFIParsePDFDocumentC =
    ffi.Int32 Function(
      ffi.Pointer<ffi.Char> filePath,
      ffi.Pointer<FFIDocumentInfo> documentInfo,
    );
typedef FFIFreeStringC = ffi.Void Function(ffi.Pointer<ffi.Char> ptr);
typedef FFIRenderPDFPageC =
    ffi.Int32 Function(
      ffi.Pointer<ffi.Char> filePath,
      ffi.Int32 pageNumber,
      ffi.Float scale,
      ffi.Pointer<FFIPageRenderResult> renderResult,
    );
typedef FFIFreeDocumentInfoC =
    ffi.Void Function(ffi.Pointer<FFIDocumentInfo> documentInfo);
typedef FFIPerformOCRC =
    ffi.Int32 Function(
      ffi.Pointer<ffi.Uint8> imageData,
      ffi.Size imageSize,
      ffi.Pointer<ffi.Char> language,
      ffi.Pointer<FFIOCRResult> ocrResult,
    );
typedef FFIFreePageRenderResultC =
    ffi.Void Function(ffi.Pointer<FFIPageRenderResult> renderResult);
typedef FFISaveDocumentC =
    ffi.Int32 Function(
      ffi.Pointer<ffi.Char> filePath,
      ffi.Pointer<ffi.Char> title,
      ffi.Pointer<ffi.Char> author,
      ffi.Int32 pageCount,
      ffi.Int64 fileSize,
      ffi.Pointer<ffi.Int64> documentId,
    );
typedef FFIGetDocumentC =
    ffi.Int32 Function(
      ffi.Int64 documentId,
      ffi.Pointer<FFIDocumentInfo> documentInfo,
    );
typedef FFISaveOCRResultC =
    ffi.Int32 Function(
      ffi.Int64 documentId,
      ffi.Int32 pageNumber,
      ffi.Pointer<ffi.Char> ocrText,
      ffi.Float confidence,
      ffi.Pointer<ffi.Int64> ocrId,
    );
typedef FFIFreeOCRResultC =
    ffi.Void Function(ffi.Pointer<FFIOCRResult> ocrResult);

/// Dart函数签名定义
typedef DartFFIInitializeC = int Function();
typedef DartFFICleanupC = int Function();
typedef DartFFIParsePDFDocumentC =
    int Function(
      ffi.Pointer<ffi.Char> filePath,
      ffi.Pointer<FFIDocumentInfo> documentInfo,
    );
typedef DartFFIRenderPDFPageC =
    int Function(
      ffi.Pointer<ffi.Char> filePath,
      int pageNumber,
      double scale,
      ffi.Pointer<FFIPageRenderResult> renderResult,
    );
typedef DartFFIPerformOCRC =
    int Function(
      ffi.Pointer<ffi.Uint8> imageData,
      int imageSize,
      ffi.Pointer<ffi.Char> language,
      ffi.Pointer<FFIOCRResult> ocrResult,
    );
typedef DartFFISaveDocumentC = int Function(
  ffi.Pointer<ffi.Char> filePath,
  ffi.Pointer<ffi.Char> title,
  ffi.Pointer<ffi.Char> author,
  int pageCount,
  int fileSize,
  ffi.Pointer<ffi.Int64> documentId,
);
typedef DartFFIGetDocumentC = int Function(
  int documentId,
  ffi.Pointer<FFIDocumentInfo> documentInfo,
);
typedef DartFFISaveOCRResultC = int Function(
  int documentId,
  int pageNumber,
  ffi.Pointer<ffi.Char> ocrText,
  double confidence,
  ffi.Pointer<ffi.Int64> ocrId,
);
typedef DartFFIFreeStringC = void Function(ffi.Pointer<ffi.Char> ptr);
typedef DartFFIFreeDocumentInfoC =
    void Function(ffi.Pointer<FFIDocumentInfo> documentInfo);
typedef DartFFIFreePageRenderResultC =
    void Function(ffi.Pointer<FFIPageRenderResult> renderResult);
typedef DartFFIFreeOCRResultC =
    void Function(ffi.Pointer<FFIOCRResult> ocrResult);

/// Rust FFI桥接类
class RustBridge {
  static RustBridge? _instance;
  late final ffi.DynamicLibrary _library;
  late final DartFFIInitializeC _ffiInitializeC;
  late final DartFFICleanupC _ffiCleanupC;
  late final DartFFIParsePDFDocumentC _ffiParsePDFDocumentC;
  late final DartFFIRenderPDFPageC _ffiRenderPDFPageC;
  late final DartFFIPerformOCRC _ffiPerformOCRC;
  late final DartFFISaveDocumentC _ffiSaveDocumentC;
  late final DartFFIGetDocumentC _ffiGetDocumentC;
  late final DartFFISaveOCRResultC _ffiSaveOCRResultC;
  late final DartFFIFreeStringC _ffiFreeStringC;
  late final DartFFIFreeDocumentInfoC _ffiFreeDocumentInfoC;
  late final DartFFIFreePageRenderResultC _ffiFreePageRenderResultC;
  late final DartFFIFreeOCRResultC _ffiFreeOCRResultC;

  bool _isInitialized = false;

  RustBridge._();

  /// 获取单例实例
  static RustBridge get instance {
    _instance ??= RustBridge._();
    return _instance!;
  }

  /// 初始化FFI桥接
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 加载动态库
      _library = _loadLibrary();

      // 绑定函数
      _bindFunctions();

      // 初始化Rust端
      final result = _ffiInitializeC();
      final errorCode = FFIErrorCode.fromValue(result);

      if (errorCode != FFIErrorCode.success) {
        throw RustBridgeException('FFI初始化失败: $errorCode');
      }

      _isInitialized = true;
    } catch (e) {
      throw RustBridgeException('FFI桥接初始化失败: $e');
    }
  }

  /// 清理FFI桥接
  Future<void> cleanup() async {
    if (!_isInitialized) return;

    try {
      final result = _ffiCleanupC();
      final errorCode = FFIErrorCode.fromValue(result);

      if (errorCode != FFIErrorCode.success) {
        print('FFI清理警告: $errorCode');
      }

      _isInitialized = false;
    } catch (e) {
      print('FFI清理错误: $e');
    }
  }

  /// 解析PDF文档
  Future<DocumentInfo> parsePDFDocument(String filePath) async {
    if (!_isInitialized) {
      throw RustBridgeException('FFI桥接未初始化');
    }

    final filePathPtr = filePath.toNativeUtf8Pointer();
    final documentInfoPtr = calloc<FFIDocumentInfo>();

    try {
      final result = _ffiParsePDFDocumentC(
        filePathPtr.cast<ffi.Char>(),
        documentInfoPtr,
      );

      final errorCode = FFIErrorCode.fromValue(result);
      if (errorCode != FFIErrorCode.success) {
        throw RustBridgeException('PDF解析失败: $errorCode');
      }

      final documentInfo = DocumentInfo.fromFFI(documentInfoPtr.ref);
      return documentInfo;
    } finally {
      // 清理内存
      _ffiFreeDocumentInfoC(documentInfoPtr);
      calloc.free(documentInfoPtr);
      calloc.free(filePathPtr);
    }
  }

  /// 渲染PDF页面
  Future<PageRenderResult> renderPDFPage(
    String filePath,
    int pageNumber, {
    double scale = 1.0,
  }) async {
    if (!_isInitialized) {
      throw RustBridgeException('FFI桥接未初始化');
    }

    if (pageNumber < 1) {
      throw RustBridgeException('页面编号必须大于0');
    }

    if (scale <= 0.0) {
      throw RustBridgeException('缩放比例必须大于0');
    }

    final filePathPtr = filePath.toNativeUtf8Pointer();
    final renderResultPtr = calloc<FFIPageRenderResult>();

    try {
      final result = _ffiRenderPDFPageC(
        filePathPtr.cast<ffi.Char>(),
        pageNumber,
        scale,
        renderResultPtr,
      );

      final errorCode = FFIErrorCode.fromValue(result);
      if (errorCode != FFIErrorCode.success) {
        throw RustBridgeException('PDF页面渲染失败: $errorCode');
      }

      final renderResult = PageRenderResult.fromFFI(renderResultPtr.ref);
      return renderResult;
    } finally {
      // 清理内存
      _ffiFreePageRenderResultC(renderResultPtr);
      calloc.free(renderResultPtr);
      calloc.free(filePathPtr);
    }
  }

  /// 执行OCR文字识别
  Future<OCRResult> performOCR(
    Uint8List imageData, {
    String language = 'auto',
  }) async {
    if (!_isInitialized) {
      throw RustBridgeException('FFI桥接未初始化');
    }

    if (imageData.isEmpty) {
      throw RustBridgeException('图像数据不能为空');
    }

    final imageDataPtr = calloc<ffi.Uint8>(imageData.length);
    final languagePtr = language.toNativeUtf8Pointer();
    final ocrResultPtr = calloc<FFIOCRResult>();

    try {
      // 复制图像数据到C内存
      for (int i = 0; i < imageData.length; i++) {
        imageDataPtr[i] = imageData[i];
      }

      final result = _ffiPerformOCRC(
        imageDataPtr,
        imageData.length,
        languagePtr.cast<ffi.Char>(),
        ocrResultPtr,
      );

      final errorCode = FFIErrorCode.fromValue(result);
      if (errorCode != FFIErrorCode.success) {
        throw RustBridgeException('OCR处理失败: $errorCode');
      }

      final ocrResult = OCRResult.fromFFI(ocrResultPtr.ref);
      return ocrResult;
    } finally {
      // 清理内存
      _ffiFreeOCRResultC(ocrResultPtr);
      calloc.free(ocrResultPtr);
      calloc.free(languagePtr);
      calloc.free(imageDataPtr);
    }
  }

  /// 保存文档信息到数据库
  Future<int> saveDocument({
    required String filePath,
    required String title,
    String? author,
    required int pageCount,
    required int fileSize,
  }) async {
    if (!_isInitialized) {
      throw RustBridgeException('FFI桥接未初始化');
    }

    final filePathPtr = filePath.toNativeUtf8Pointer();
    final titlePtr = title.toNativeUtf8Pointer();
    final authorPtr = author?.toNativeUtf8Pointer();
    final documentIdPtr = calloc<ffi.Int64>();

    try {
      final result = _ffiSaveDocumentC(
        filePathPtr.cast<ffi.Char>(),
        titlePtr.cast<ffi.Char>(),
        authorPtr?.cast<ffi.Char>() ?? ffi.nullptr,
        pageCount,
        fileSize,
        documentIdPtr,
      );

      final errorCode = FFIErrorCode.fromValue(result);
      if (errorCode != FFIErrorCode.success) {
        throw RustBridgeException('保存文档失败: $errorCode');
      }

      final documentId = documentIdPtr.value;
      return documentId;
    } finally {
      // 清理内存
      calloc.free(documentIdPtr);
      if (authorPtr != null) calloc.free(authorPtr);
      calloc.free(titlePtr);
      calloc.free(filePathPtr);
    }
  }

  /// 从数据库获取文档信息
  Future<DocumentInfo> getDocument(int documentId) async {
    if (!_isInitialized) {
      throw RustBridgeException('FFI桥接未初始化');
    }

    final documentInfoPtr = calloc<FFIDocumentInfo>();

    try {
      final result = _ffiGetDocumentC(documentId, documentInfoPtr);

      final errorCode = FFIErrorCode.fromValue(result);
      if (errorCode != FFIErrorCode.success) {
        throw RustBridgeException('获取文档失败: $errorCode');
      }

      final documentInfo = DocumentInfo.fromFFI(documentInfoPtr.ref);
      return documentInfo;
    } finally {
      // 清理内存
      _ffiFreeDocumentInfoC(documentInfoPtr);
      calloc.free(documentInfoPtr);
    }
  }

  /// 保存OCR结果到数据库
  Future<int> saveOCRResult({
    required int documentId,
    required int pageNumber,
    required String ocrText,
    required double confidence,
  }) async {
    if (!_isInitialized) {
      throw RustBridgeException('FFI桥接未初始化');
    }

    final ocrTextPtr = ocrText.toNativeUtf8Pointer();
    final ocrIdPtr = calloc<ffi.Int64>();

    try {
      final result = _ffiSaveOCRResultC(
        documentId,
        pageNumber,
        ocrTextPtr.cast<ffi.Char>(),
        confidence,
        ocrIdPtr,
      );

      final errorCode = FFIErrorCode.fromValue(result);
      if (errorCode != FFIErrorCode.success) {
        throw RustBridgeException('保存OCR结果失败: $errorCode');
      }

      final ocrId = ocrIdPtr.value;
      return ocrId;
    } finally {
      // 清理内存
      calloc.free(ocrIdPtr);
      calloc.free(ocrTextPtr);
    }
  }

  /// 加载动态库
  ffi.DynamicLibrary _loadLibrary() {
    if (Platform.isAndroid) {
      return ffi.DynamicLibrary.open('libpdf_reader_core.so');
    } else if (Platform.isIOS) {
      return ffi.DynamicLibrary.process();
    } else if (Platform.isWindows) {
      return ffi.DynamicLibrary.open('pdf_reader_core.dll');
    } else if (Platform.isLinux) {
      return ffi.DynamicLibrary.open('libpdf_reader_core.so');
    } else if (Platform.isMacOS) {
      return ffi.DynamicLibrary.open('libpdf_reader_core.dylib');
    } else {
      throw UnsupportedError('不支持的平台: ${Platform.operatingSystem}');
    }
  }

  /// 绑定FFI函数
  void _bindFunctions() {
    _ffiInitializeC = _library
        .lookup<ffi.NativeFunction<FFIInitializeC>>('ffi_initialize_c')
        .asFunction<DartFFIInitializeC>();

    _ffiCleanupC = _library
        .lookup<ffi.NativeFunction<FFICleanupC>>('ffi_cleanup_c')
        .asFunction<DartFFICleanupC>();

    _ffiParsePDFDocumentC = _library
        .lookup<ffi.NativeFunction<FFIParsePDFDocumentC>>(
          'ffi_parse_pdf_document_c',
        )
        .asFunction<DartFFIParsePDFDocumentC>();

    _ffiRenderPDFPageC = _library
        .lookup<ffi.NativeFunction<FFIRenderPDFPageC>>('ffi_render_pdf_page_c')
        .asFunction<DartFFIRenderPDFPageC>();

    _ffiPerformOCRC = _library
        .lookup<ffi.NativeFunction<FFIPerformOCRC>>('ffi_perform_ocr_c')
        .asFunction<DartFFIPerformOCRC>();

    _ffiSaveDocumentC = _library
        .lookup<ffi.NativeFunction<FFISaveDocumentC>>('ffi_save_document_c')
        .asFunction<DartFFISaveDocumentC>();

    _ffiGetDocumentC = _library
        .lookup<ffi.NativeFunction<FFIGetDocumentC>>('ffi_get_document_c')
        .asFunction<DartFFIGetDocumentC>();

    _ffiSaveOCRResultC = _library
        .lookup<ffi.NativeFunction<FFISaveOCRResultC>>('ffi_save_ocr_result_c')
        .asFunction<DartFFISaveOCRResultC>();

    _ffiFreeStringC = _library
        .lookup<ffi.NativeFunction<FFIFreeStringC>>('ffi_free_string_c')
        .asFunction<DartFFIFreeStringC>();

    _ffiFreeDocumentInfoC = _library
        .lookup<ffi.NativeFunction<FFIFreeDocumentInfoC>>(
          'ffi_free_document_info_c',
        )
        .asFunction<DartFFIFreeDocumentInfoC>();

    _ffiFreePageRenderResultC = _library
        .lookup<ffi.NativeFunction<FFIFreePageRenderResultC>>(
          'ffi_free_page_render_result_c',
        )
        .asFunction<DartFFIFreePageRenderResultC>();

    _ffiFreeOCRResultC = _library
        .lookup<ffi.NativeFunction<FFIFreeOCRResultC>>('ffi_free_ocr_result_c')
        .asFunction<DartFFIFreeOCRResultC>();
  }
}

/// Rust桥接异常类
class RustBridgeException implements Exception {
  final String message;

  const RustBridgeException(this.message);

  @override
  String toString() => 'RustBridgeException: $message';
}

/// 扩展方法：C字符串转Dart字符串
extension CStringToDart on ffi.Pointer<ffi.Char> {
  String toDartString() {
    if (address == 0) return '';
    return cast<Utf8>().toDartString();
  }
}

/// 扩展方法：Dart字符串转C字符串
extension DartStringToNative on String {
  ffi.Pointer<Utf8> toNativeUtf8Pointer() {
    return toNativeUtf8();
  }
}
