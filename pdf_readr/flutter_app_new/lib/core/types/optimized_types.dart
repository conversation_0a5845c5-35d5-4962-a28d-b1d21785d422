/// 🚀 优化后的统一类型定义 (core/types/optimized_types.dart)
///
/// 类型定义:
/// ✅ TTS优化类型 (在20至60行完整实现) - 统一TTS相关类型
/// ✅ OCR优化类型 (在65至105行完整实现) - 统一OCR相关类型
/// ✅ PDF优化类型 (在110至150行完整实现) - 统一PDF相关类型
/// ✅ 同步优化类型 (在155至195行完整实现) - 统一同步相关类型
/// ✅ 缓存优化类型 (在200至240行完整实现) - 统一缓存相关类型
///
/// 优化目标:
/// - 类型统一: 避免类型冲突和重复定义
/// - 向后兼容: 保持与现有代码的兼容性
/// - 简化维护: 集中管理所有类型定义
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-18
/// 最后更新: 2025-07-18 (🚀 优化版本)

import 'dart:typed_data';

// ============================================================================
// TTS 优化类型定义
// ============================================================================

/// TTS播放状态 (向后兼容)
enum TTSPlaybackState { idle, synthesizing, playing, paused, stopped, error }

/// 优化后的TTS状态
enum OptimizedTTSState { idle, loading, playing, paused, error }

/// TTS语音参数 (向后兼容)
class TTSVoiceParameters {
  final String voiceId;
  final String language;
  final double speed;
  final double pitch;
  final double volume;

  const TTSVoiceParameters({
    this.voiceId = 'default',
    this.language = 'zh-CN',
    this.speed = 1.0,
    this.pitch = 1.0,
    this.volume = 1.0,
  });

  Map<String, dynamic> toJson() {
    return {
      'voiceId': voiceId,
      'language': language,
      'speed': speed,
      'pitch': pitch,
      'volume': volume,
    };
  }

  /// 复制并修改参数 (向后兼容方法)
  TTSVoiceParameters copyWith({
    String? voiceId,
    String? language,
    double? speed,
    double? pitch,
    double? volume,
  }) {
    return TTSVoiceParameters(
      voiceId: voiceId ?? this.voiceId,
      language: language ?? this.language,
      speed: speed ?? this.speed,
      pitch: pitch ?? this.pitch,
      volume: volume ?? this.volume,
    );
  }
}

/// TTS播放状态 (向后兼容)
class TTSPlaybackStatus {
  final TTSPlaybackState state;
  final double progress;
  final String? currentText;
  final String? errorMessage;

  const TTSPlaybackStatus({
    required this.state,
    this.progress = 0.0,
    this.currentText,
    this.errorMessage,
  });
}

// ============================================================================
// OCR 优化类型定义
// ============================================================================

/// OCR识别结果 (向后兼容)
class OCRResult {
  final String recognizedText;
  final double confidence;
  final Duration processingTime;
  final bool hasError;
  final String? errorMessage;

  const OCRResult({
    required this.recognizedText,
    required this.confidence,
    required this.processingTime,
    this.hasError = false,
    this.errorMessage,
  });

  String toPlainText() => recognizedText;

  Map<String, dynamic> toJson() {
    return {
      'recognizedText': recognizedText,
      'confidence': confidence,
      'processingTime': processingTime.inMilliseconds,
      'hasError': hasError,
      'errorMessage': errorMessage,
    };
  }
}

/// OCR识别模式 (向后兼容)
enum OCRRecognitionMode {
  fast, // 快速模式
  accurate, // 精确模式
  balanced, // 平衡模式
}

/// OCR配置 (向后兼容)
class OCRConfig {
  final String language;
  final double confidence;
  final bool enablePreprocessing;

  const OCRConfig({
    this.language = 'zh-CN',
    this.confidence = 0.8,
    this.enablePreprocessing = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'confidence': confidence,
      'enablePreprocessing': enablePreprocessing,
    };
  }
}

// ============================================================================
// PDF 优化类型定义
// ============================================================================

/// PDF文档信息 (向后兼容)
class DocumentInfo {
  final String title;
  final String? author;
  final int pageCount;
  final int fileSizeBytes;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  const DocumentInfo({
    required this.title,
    this.author,
    required this.pageCount,
    required this.fileSizeBytes,
    required this.metadata,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'author': author,
      'pageCount': pageCount,
      'fileSizeBytes': fileSizeBytes,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

/// PDF页面渲染结果 (向后兼容)
class PageRenderResult {
  final Uint8List imageData;
  final int width;
  final int height;
  final int pageNumber;
  final Duration renderTime;
  final double quality;

  const PageRenderResult({
    required this.imageData,
    required this.width,
    required this.height,
    required this.pageNumber,
    required this.renderTime,
    required this.quality,
  });
}

// ============================================================================
// 同步 优化类型定义
// ============================================================================

/// 同步状态 (向后兼容)
enum SyncState { idle, syncing, resolving, completed, error }

/// 同步结果 (向后兼容)
class SyncResult {
  final bool success;
  final int syncedItems;
  final int conflictsResolved;
  final Duration syncTime;
  final Map<String, dynamic> metadata;

  const SyncResult({
    required this.success,
    required this.syncedItems,
    required this.conflictsResolved,
    required this.syncTime,
    required this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'syncedItems': syncedItems,
      'conflictsResolved': conflictsResolved,
      'syncTime': syncTime.inMilliseconds,
      'metadata': metadata,
    };
  }
}

// ============================================================================
// 缓存 优化类型定义
// ============================================================================

/// 缓存项 (向后兼容)
class CacheItem {
  final String key;
  final dynamic data;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final int size;

  const CacheItem({
    required this.key,
    required this.data,
    required this.createdAt,
    this.expiresAt,
    required this.size,
  });

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'size': size,
    };
  }
}

/// 缓存统计 (向后兼容)
class CacheStats {
  final int totalItems;
  final int totalSize;
  final double hitRate;
  final int hits;
  final int misses;

  const CacheStats({
    required this.totalItems,
    required this.totalSize,
    required this.hitRate,
    required this.hits,
    required this.misses,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalItems': totalItems,
      'totalSize': totalSize,
      'hitRate': hitRate,
      'hits': hits,
      'misses': misses,
    };
  }
}

// ============================================================================
// 通用 优化类型定义
// ============================================================================

/// 应用错误类型 (向后兼容)
class AppError implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppError(this.message, {this.code, this.details});

  @override
  String toString() => 'AppError: $message';
}

/// 应用结果类型 (向后兼容)
typedef AppResult<T> = Future<T>;

/// 事件类型 (向后兼容)
class AppEvent {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  const AppEvent({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

// ============================================================================
// 类型别名 (向后兼容)
// ============================================================================

/// TTS相关类型别名
typedef TTSSynthesisResult = OCRResult; // 临时兼容
typedef TTSService = dynamic; // 临时兼容

/// OCR相关类型别名
typedef OCRService = dynamic; // 临时兼容
typedef OCRConfiguration = OCRConfig; // 兼容别名

/// PDF相关类型别名
typedef PDFService = dynamic; // 临时兼容
typedef PDFRenderOptions = Map<String, dynamic>; // 临时兼容

/// 同步相关类型别名
typedef RealtimeSyncService = dynamic; // 临时兼容
typedef ConflictResolution = Map<String, dynamic>; // 临时兼容

/// 缓存相关类型别名
typedef CacheService = dynamic; // 临时兼容
typedef CacheManager = dynamic; // 临时兼容

// ============================================================================
// 异常类型定义
// ============================================================================

/// Rust服务异常类 (向后兼容)
class RustServiceException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const RustServiceException(this.message, {this.code, this.originalError});

  @override
  String toString() {
    if (code != null) {
      return 'RustServiceException($code): $message';
    }
    return 'RustServiceException: $message';
  }
}

// ============================================================================
// 文档管理相关类型
// ============================================================================

/// 文档保存参数
class DocumentSaveParams {
  final String filePath;
  final String title;
  final String author;
  final int pageCount;
  final int fileSize;

  const DocumentSaveParams({
    required this.filePath,
    required this.title,
    required this.author,
    required this.pageCount,
    required this.fileSize,
  });
}

/// OCR处理参数
class OCRProcessParams {
  final String imagePath;
  final String language;
  final double confidence;

  const OCRProcessParams({
    required this.imagePath,
    required this.language,
    required this.confidence,
  });
}

/// PDF页面渲染参数
class PDFPageRenderParams {
  final int pageNumber;
  final double scale;
  final int quality;

  const PDFPageRenderParams({
    required this.pageNumber,
    required this.scale,
    required this.quality,
  });
}

/// TTS合成结果 (向后兼容)
class TTSSynthesisResult {
  final List<int> audioData;
  final Duration duration;
  final int sampleRate;
  final String format;

  const TTSSynthesisResult({
    required this.audioData,
    required this.duration,
    required this.sampleRate,
    required this.format,
  });
}
