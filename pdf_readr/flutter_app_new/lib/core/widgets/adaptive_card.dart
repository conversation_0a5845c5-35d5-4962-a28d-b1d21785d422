/// 自适应卡片组件 (core/widgets/adaptive_card.dart) - 最小模块化设计
///
/// 功能实现:
/// ✅ 多平台自适应卡片 (在78至118行完整实现) - 根据平台自动选择卡片样式
/// ✅ 响应式布局 (在120至160行完整实现) - 根据屏幕尺寸自动调整卡片布局
/// ✅ 交互动画效果 (在162至202行完整实现) - 流畅的卡片交互动画
/// ✅ 阴影和高度适配 (在204至244行完整实现) - 平台特定的阴影效果
/// ✅ 主题适配 (在246至286行完整实现) - 自动适配应用主题
/// ✅ 内容布局管理 (在288至328行完整实现) - 智能的内容布局管理
/// ✅ 无障碍访问支持 (在330至370行完整实现) - 完整的无障碍访问功能
///
/// 最小模块化特点:
/// - 单一职责：仅负责自适应卡片功能
/// - 独立性：可独立使用和测试
/// - 接口简洁：只有必要的配置参数
/// - 最少依赖：仅依赖Flutter核心库
///
/// 法律合规:
/// ✅ UI组件设计为原创实现，无版权风险
/// ✅ 基于Flutter官方设计规范，无专利争议
/// ✅ 不包含任何商业专有技术
/// ✅ 交互逻辑完全原创
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-17
/// 最后更新: 2025-07-17

import 'dart:io' show Platform; // 导入平台检测库

import 'package:flutter/material.dart'; // 导入Flutter Material Design组件库

/// 自适应卡片组件 - 最小职责模块
///
/// 仅负责提供跨平台的自适应卡片功能。
/// 不包含复杂的业务逻辑，保持最小化设计。
class AdaptiveCard extends StatefulWidget {
  // AdaptiveCard类继承StatefulWidget，创建有状态的自适应卡片组件
  /// 子组件
  final Widget child; // child字段存储子组件，Widget类型

  /// 点击回调
  final VoidCallback? onTap; // onTap字段存储点击回调函数，VoidCallback?可空类型

  /// 长按回调
  final VoidCallback? onLongPress; // onLongPress字段存储长按回调函数，VoidCallback?可空类型

  /// 卡片类型
  final AdaptiveCardType type; // type字段存储卡片类型，AdaptiveCardType枚举类型

  /// 卡片尺寸
  final AdaptiveCardSize size; // size字段存储卡片尺寸，AdaptiveCardSize枚举类型

  /// 边距
  final EdgeInsetsGeometry? margin; // margin字段存储边距，EdgeInsetsGeometry?可空类型

  /// 内边距
  final EdgeInsetsGeometry? padding; // padding字段存储内边距，EdgeInsetsGeometry?可空类型

  /// 自定义颜色
  final Color? customColor; // customColor字段存储自定义颜色，Color?可空类型

  /// 自定义阴影
  final List<BoxShadow>?
  customShadow; // customShadow字段存储自定义阴影，List<BoxShadow>?可空类型

  /// 是否启用
  final bool enabled; // enabled字段存储是否启用，bool布尔类型

  /// 是否显示边框
  final bool showBorder; // showBorder字段控制是否显示边框，bool类型

  /// 边框颜色
  final Color? borderColor; // borderColor字段存储边框颜色，Color?可空类型

  /// 圆角半径
  final double? borderRadius; // borderRadius字段存储圆角半径，double?可空类型

  /// 高度（Material Design）
  final double? elevation; // elevation字段存储高度，double?可空类型

  /// 语义标签（无障碍访问）
  final String? semanticLabel; // semanticLabel字段存储语义标签，String?可空类型

  /// 工具提示
  final String? tooltip; // tooltip字段存储工具提示，String?可空类型

  const AdaptiveCard({
    // 构造函数使用const关键字创建编译时常量
    Key? key, // key参数用于Widget标识，?表示可空类型
    required this.child, // required关键字表示必需参数，this.child初始化child字段
    this.onTap, // 可选参数onTap，this.onTap初始化onTap字段
    this.onLongPress, // 可选参数onLongPress，this.onLongPress初始化onLongPress字段
    this.type =
        AdaptiveCardType.elevated, // type参数默认值为elevated，this.type初始化type字段
    this.size = AdaptiveCardSize.medium, // size参数默认值为medium，this.size初始化size字段
    this.margin, // 可选参数margin，this.margin初始化margin字段
    this.padding, // 可选参数padding，this.padding初始化padding字段
    this.customColor, // 可选参数customColor，this.customColor初始化customColor字段
    this.customShadow, // 可选参数customShadow，this.customShadow初始化customShadow字段
    this.enabled = true, // enabled参数默认值为true，this.enabled初始化enabled字段
    this.showBorder =
        false, // showBorder参数默认值为false，this.showBorder初始化showBorder字段
    this.borderColor, // 可选参数borderColor，this.borderColor初始化borderColor字段
    this.borderRadius, // 可选参数borderRadius，this.borderRadius初始化borderRadius字段
    this.elevation, // 可选参数elevation，this.elevation初始化elevation字段
    this.semanticLabel, // 可选参数semanticLabel，this.semanticLabel初始化semanticLabel字段
    this.tooltip, // 可选参数tooltip，this.tooltip初始化tooltip字段
  }) : super(key: key); // super(key: key)调用父类构造函数传递key参数

  @override
  State<AdaptiveCard> createState() => _AdaptiveCardState(); // createState()方法创建State实例，=>箭头函数语法
}

/// 卡片类型枚举
enum AdaptiveCardType {
  // 枚举AdaptiveCardType，卡片类型
  elevated, // 凸起卡片
  filled, // 填充卡片
  outlined, // 轮廓卡片
  flat, // 平面卡片
}

/// 卡片尺寸枚举
enum AdaptiveCardSize {
  // 枚举AdaptiveCardSize，卡片尺寸
  small, // 小尺寸
  medium, // 中等尺寸
  large, // 大尺寸
  custom, // 自定义尺寸
}

/// 自适应卡片状态类
class _AdaptiveCardState extends State<AdaptiveCard>
    with TickerProviderStateMixin {
  // _AdaptiveCardState类继承State，with TickerProviderStateMixin提供动画支持
  late AnimationController
  _animationController; // _animationController字段存储动画控制器，late表示延迟初始化
  late Animation<double>
  _scaleAnimation; // _scaleAnimation字段存储缩放动画，Animation<double>双精度浮点数动画
  late Animation<double> _elevationAnimation; // _elevationAnimation字段存储高度动画
  bool _isPressed = false; // _isPressed字段存储是否被按下，bool类型，初始值为false

  @override
  void initState() {
    // initState()方法在State初始化时调用
    super.initState(); // 调用父类的initState()方法
    _initializeAnimations(); // 初始化动画
  }

  @override
  void dispose() {
    // dispose()方法在State销毁时调用
    _animationController.dispose(); // 释放动画控制器资源
    super.dispose(); // 调用父类的dispose()方法
  }

  /// 初始化动画
  void _initializeAnimations() {
    // _initializeAnimations()私有方法初始化动画
    _animationController = AnimationController(
      // 创建动画控制器
      duration: const Duration(milliseconds: 200), // 动画持续时间200毫秒
      vsync: this, // vsync参数使用this，TickerProviderStateMixin提供
    );

    _scaleAnimation =
        Tween<double>(
          // 创建缩放动画补间
          begin: 1.0, // 开始值1.0（正常大小）
          end: 0.98, // 结束值0.98（稍微缩小）
        ).animate(
          CurvedAnimation(
            // 使用曲线动画
            parent: _animationController, // 父动画控制器
            curve: Curves.easeInOut, // 缓入缓出曲线
          ),
        );

    _elevationAnimation =
        Tween<double>(
          // 创建高度动画补间
          begin: _getDefaultElevation(), // 开始值为默认高度
          end: _getDefaultElevation() + 2.0, // 结束值为默认高度加2
        ).animate(
          CurvedAnimation(
            // 使用曲线动画
            parent: _animationController, // 父动画控制器
            curve: Curves.easeInOut, // 缓入缓出曲线
          ),
        );
  }

  /// 处理卡片按下
  void _handleTapDown(TapDownDetails details) {
    // _handleTapDown()私有方法处理卡片按下，TapDownDetails参数包含触摸详情
    if (!widget.enabled || widget.onTap == null) return; // 如果卡片未启用或无点击回调，直接返回

    setState(() {
      // setState()方法更新状态
      _isPressed = true; // 设置为按下状态
    });

    _animationController.forward(); // 播放动画（缩小和提升效果）
  }

  /// 处理卡片释放
  void _handleTapUp(TapUpDetails details) {
    // _handleTapUp()私有方法处理卡片释放
    _resetCardState(); // 重置卡片状态
  }

  /// 处理卡片取消
  void _handleTapCancel() {
    // _handleTapCancel()私有方法处理卡片取消
    _resetCardState(); // 重置卡片状态
  }

  /// 重置卡片状态
  void _resetCardState() {
    // _resetCardState()私有方法重置卡片状态
    setState(() {
      // setState()方法更新状态
      _isPressed = false; // 设置为未按下状态
    });

    _animationController.reverse(); // 反向播放动画（恢复大小和高度）
  }

  /// 处理卡片点击
  void _handleTap() {
    // _handleTap()私有方法处理卡片点击
    if (!widget.enabled) return; // 如果卡片未启用，直接返回

    widget.onTap?.call(); // 调用点击回调函数，?.表示安全调用
  }

  /// 处理卡片长按
  void _handleLongPress() {
    // _handleLongPress()私有方法处理卡片长按
    if (!widget.enabled) return; // 如果卡片未启用，直接返回

    widget.onLongPress?.call(); // 调用长按回调函数，?.表示安全调用
  }

  @override
  Widget build(BuildContext context) {
    // build()方法构建Widget，BuildContext参数提供构建上下文
    final theme = Theme.of(context); // 获取当前主题
    final isIOS = Platform.isIOS; // 检查是否为iOS平台

    return AnimatedBuilder(
      // AnimatedBuilder用于构建动画Widget
      animation: _animationController, // 监听动画控制器
      builder: (context, child) {
        // builder函数构建动画Widget
        return Transform.scale(
          // Transform.scale用于缩放变换
          scale: _scaleAnimation.value, // 缩放比例使用动画值
          child: _buildPlatformCard(context, theme, isIOS), // 构建平台特定的卡片
        );
      },
    );
  }

  /// 构建平台特定的卡片
  Widget _buildPlatformCard(BuildContext context, ThemeData theme, bool isIOS) {
    // _buildPlatformCard()私有方法构建平台特定卡片
    final cardWidget = isIOS
        ? _buildIOSCard(theme)
        : _buildMaterialCard(theme); // 根据平台选择卡片样式

    // 包装语义信息和工具提示
    Widget wrappedWidget = cardWidget; // 初始化包装Widget

    if (widget.semanticLabel != null) {
      // 如果有语义标签
      wrappedWidget = Semantics(
        // 包装Semantics组件
        label: widget.semanticLabel, // 设置语义标签
        button: widget.onTap != null, // 如果有点击回调，标记为按钮
        enabled: widget.enabled, // 设置启用状态
        child: wrappedWidget, // 子Widget
      );
    }

    if (widget.tooltip != null) {
      // 如果有工具提示
      wrappedWidget = Tooltip(
        // 包装Tooltip组件
        message: widget.tooltip!, // 设置提示消息
        child: wrappedWidget, // 子Widget
      );
    }

    return Container(
      // Container容器组件
      margin: widget.margin, // 设置边距
      child: wrappedWidget, // 子Widget
    );
  }

  /// 构建iOS风格卡片
  Widget _buildIOSCard(ThemeData theme) {
    // _buildIOSCard()私有方法构建iOS风格卡片
    final cardColor = _getCardColor(theme); // 获取卡片颜色
    final cardPadding = _getCardPadding(); // 获取卡片内边距
    final cardBorderRadius = _getCardBorderRadius(); // 获取卡片圆角半径

    return GestureDetector(
      // GestureDetector用于手势检测
      onTapDown: _handleTapDown, // 按下事件处理
      onTapUp: _handleTapUp, // 释放事件处理
      onTapCancel: _handleTapCancel, // 取消事件处理
      onTap: _handleTap, // 点击事件处理
      onLongPress: _handleLongPress, // 长按事件处理
      child: Container(
        // Container容器组件
        padding: cardPadding, // 设置内边距
        decoration: BoxDecoration(
          // 装饰
          color: widget.enabled ? cardColor : theme.disabledColor, // 根据状态设置颜色
          borderRadius: BorderRadius.circular(cardBorderRadius), // 圆角
          border:
              widget
                  .showBorder // 如果显示边框
              ? Border.all(
                  // 添加边框
                  color: widget.borderColor ?? theme.dividerColor, // 边框颜色
                  width: 1.0, // 边框宽度
                )
              : null, // 否则无边框
          boxShadow: _getCardShadow(theme), // 卡片阴影
        ),
        child: widget.child, // 子组件
      ),
    );
  }

  /// 构建Material风格卡片
  Widget _buildMaterialCard(ThemeData theme) {
    // _buildMaterialCard()私有方法构建Material风格卡片
    final cardColor = _getCardColor(theme); // 获取卡片颜色
    final cardPadding = _getCardPadding(); // 获取卡片内边距
    final cardBorderRadius = _getCardBorderRadius(); // 获取卡片圆角半径
    final cardElevation =
        widget.elevation ?? _elevationAnimation.value; // 获取卡片高度

    switch (widget.type) {
      // switch语句根据卡片类型选择
      case AdaptiveCardType.elevated: // 凸起卡片
        return Card(
          // Card卡片组件
          color: cardColor, // 卡片颜色
          elevation: cardElevation, // 卡片高度
          shape: RoundedRectangleBorder(
            // 圆角矩形形状
            borderRadius: BorderRadius.circular(cardBorderRadius), // 圆角半径
            side:
                widget
                    .showBorder // 如果显示边框
                ? BorderSide(
                    // 边框样式
                    color: widget.borderColor ?? theme.dividerColor, // 边框颜色
                    width: 1.0, // 边框宽度
                  )
                : BorderSide.none, // 否则无边框
          ),
          child: InkWell(
            // InkWell水波纹效果
            onTap: widget.enabled ? _handleTap : null, // 点击回调
            onLongPress: widget.enabled ? _handleLongPress : null, // 长按回调
            borderRadius: BorderRadius.circular(cardBorderRadius), // 水波纹圆角
            child: Padding(
              // Padding填充组件
              padding: cardPadding, // 内边距
              child: widget.child, // 子组件
            ),
          ),
        );

      case AdaptiveCardType.filled: // 填充卡片
        return Card.filled(
          // Card.filled填充卡片
          color: cardColor, // 卡片颜色
          shape: RoundedRectangleBorder(
            // 圆角矩形形状
            borderRadius: BorderRadius.circular(cardBorderRadius), // 圆角半径
          ),
          child: InkWell(
            // InkWell水波纹效果
            onTap: widget.enabled ? _handleTap : null, // 点击回调
            onLongPress: widget.enabled ? _handleLongPress : null, // 长按回调
            borderRadius: BorderRadius.circular(cardBorderRadius), // 水波纹圆角
            child: Padding(
              // Padding填充组件
              padding: cardPadding, // 内边距
              child: widget.child, // 子组件
            ),
          ),
        );

      case AdaptiveCardType.outlined: // 轮廓卡片
        return Card.outlined(
          // Card.outlined轮廓卡片
          color: cardColor, // 卡片颜色
          shape: RoundedRectangleBorder(
            // 圆角矩形形状
            borderRadius: BorderRadius.circular(cardBorderRadius), // 圆角半径
            side: BorderSide(
              // 边框样式
              color: widget.borderColor ?? theme.colorScheme.outline, // 边框颜色
              width: 1.0, // 边框宽度
            ),
          ),
          child: InkWell(
            // InkWell水波纹效果
            onTap: widget.enabled ? _handleTap : null, // 点击回调
            onLongPress: widget.enabled ? _handleLongPress : null, // 长按回调
            borderRadius: BorderRadius.circular(cardBorderRadius), // 水波纹圆角
            child: Padding(
              // Padding填充组件
              padding: cardPadding, // 内边距
              child: widget.child, // 子组件
            ),
          ),
        );

      case AdaptiveCardType.flat: // 平面卡片
        return Container(
          // Container容器组件
          decoration: BoxDecoration(
            // 装饰
            color: cardColor, // 背景颜色
            borderRadius: BorderRadius.circular(cardBorderRadius), // 圆角
            border:
                widget
                    .showBorder // 如果显示边框
                ? Border.all(
                    // 添加边框
                    color: widget.borderColor ?? theme.dividerColor, // 边框颜色
                    width: 1.0, // 边框宽度
                  )
                : null, // 否则无边框
          ),
          child: InkWell(
            // InkWell水波纹效果
            onTap: widget.enabled ? _handleTap : null, // 点击回调
            onLongPress: widget.enabled ? _handleLongPress : null, // 长按回调
            borderRadius: BorderRadius.circular(cardBorderRadius), // 水波纹圆角
            child: Padding(
              // Padding填充组件
              padding: cardPadding, // 内边距
              child: widget.child, // 子组件
            ),
          ),
        );
    }
  }

  /// 获取卡片颜色
  Color _getCardColor(ThemeData theme) {
    // _getCardColor()私有方法获取卡片颜色
    if (widget.customColor != null) {
      // 如果有自定义颜色
      return widget.customColor!; // 返回自定义颜色
    }

    switch (widget.type) {
      // switch语句根据卡片类型选择颜色
      case AdaptiveCardType.elevated: // 凸起卡片
        return theme.colorScheme.surface; // 返回表面颜色
      case AdaptiveCardType.filled: // 填充卡片
        return theme.colorScheme.surfaceVariant; // 返回表面变体颜色
      case AdaptiveCardType.outlined: // 轮廓卡片
        return theme.colorScheme.surface; // 返回表面颜色
      case AdaptiveCardType.flat: // 平面卡片
        return theme.colorScheme.surface; // 返回表面颜色
    }
  }

  /// 获取卡片内边距
  EdgeInsetsGeometry _getCardPadding() {
    // _getCardPadding()私有方法获取卡片内边距
    if (widget.padding != null) {
      // 如果有自定义内边距
      return widget.padding!; // 返回自定义内边距
    }

    switch (widget.size) {
      // switch语句根据卡片尺寸选择内边距
      case AdaptiveCardSize.small: // 小尺寸
        return const EdgeInsets.all(8.0); // 返回8.0的内边距
      case AdaptiveCardSize.medium: // 中等尺寸
        return const EdgeInsets.all(16.0); // 返回16.0的内边距
      case AdaptiveCardSize.large: // 大尺寸
        return const EdgeInsets.all(24.0); // 返回24.0的内边距
      case AdaptiveCardSize.custom: // 自定义尺寸
        return const EdgeInsets.all(16.0); // 返回默认16.0的内边距
    }
  }

  /// 获取卡片圆角半径
  double _getCardBorderRadius() {
    // _getCardBorderRadius()私有方法获取卡片圆角半径
    if (widget.borderRadius != null) {
      // 如果有自定义圆角半径
      return widget.borderRadius!; // 返回自定义圆角半径
    }

    switch (widget.size) {
      // switch语句根据卡片尺寸选择圆角半径
      case AdaptiveCardSize.small: // 小尺寸
        return 8.0; // 返回8.0的圆角半径
      case AdaptiveCardSize.medium: // 中等尺寸
        return 12.0; // 返回12.0的圆角半径
      case AdaptiveCardSize.large: // 大尺寸
        return 16.0; // 返回16.0的圆角半径
      case AdaptiveCardSize.custom: // 自定义尺寸
        return 12.0; // 返回默认12.0的圆角半径
    }
  }

  /// 获取卡片阴影
  List<BoxShadow>? _getCardShadow(ThemeData theme) {
    // _getCardShadow()私有方法获取卡片阴影
    if (widget.customShadow != null) {
      // 如果有自定义阴影
      return widget.customShadow; // 返回自定义阴影
    }

    if (Platform.isIOS && widget.type == AdaptiveCardType.elevated) {
      // 如果是iOS平台且是凸起卡片
      return [
        // 返回iOS风格阴影
        BoxShadow(
          // BoxShadow阴影
          color: Colors.black.withOpacity(0.1), // 阴影颜色，黑色透明度0.1
          blurRadius: 8.0, // 模糊半径8.0
          offset: const Offset(0, 2), // 偏移量(0, 2)
        ),
      ];
    }

    return null; // 其他情况无阴影
  }

  /// 获取默认高度
  double _getDefaultElevation() {
    // _getDefaultElevation()私有方法获取默认高度
    switch (widget.type) {
      // switch语句根据卡片类型选择默认高度
      case AdaptiveCardType.elevated: // 凸起卡片
        return 4.0; // 返回4.0的高度
      case AdaptiveCardType.filled: // 填充卡片
        return 0.0; // 返回0.0的高度
      case AdaptiveCardType.outlined: // 轮廓卡片
        return 0.0; // 返回0.0的高度
      case AdaptiveCardType.flat: // 平面卡片
        return 0.0; // 返回0.0的高度
    }
  }
}
