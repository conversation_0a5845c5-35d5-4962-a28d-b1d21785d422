# 🚀 Rust动态库构建配置指南

## 📋 构建配置概述

**目标**: 构建PDF阅读器的Rust核心动态库，支持FFI调用
**平台**: Windows (主要), Linux, macOS (跨平台支持)
**输出**: `pdf_reader_core.dll` (Windows), `libpdf_reader_core.so` (Linux), `libpdf_reader_core.dylib` (macOS)

---

## 🔧 Cargo.toml配置

### **主要配置文件**
```toml
# rust_core/Cargo.toml
[package]
name = "pdf_reader_core"
version = "0.1.0"
edition = "2021"

[lib]
name = "pdf_reader_core"
crate-type = ["cdylib"]  # 生成C兼容的动态库

[dependencies]
# PDF处理
lopdf = "0.26.0"
pdf-extract = "0.6.4"

# OCR处理
tesseract = "0.13.0"
image = "0.24.0"

# TTS处理
tts = "0.25.0"
rodio = "0.17.0"

# 数据库
rusqlite = { version = "0.29.0", features = ["bundled"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 异步处理
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"

# FFI支持
libc = "0.2"
once_cell = "1.19"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
log = "0.4"
env_logger = "0.10"

# 缓存
lru = "0.12"

# 同步
parking_lot = "0.12"

[build-dependencies]
cbindgen = "0.24"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
```

---

## 🏗️ 构建脚本配置

### **build.rs构建脚本**
```rust
// rust_core/build.rs
use std::env;
use std::path::PathBuf;

fn main() {
    let crate_dir = env::var("CARGO_MANIFEST_DIR").unwrap();
    let package_name = env::var("CARGO_PKG_NAME").unwrap();
    let output_dir = target_dir()
        .join(env::var("PROFILE").unwrap())
        .join("include");

    std::fs::create_dir_all(&output_dir).unwrap();

    // 生成C头文件
    cbindgen::Builder::new()
        .with_crate(crate_dir)
        .with_language(cbindgen::Language::C)
        .with_pragma_once(true)
        .with_include_guard("PDF_READER_CORE_H")
        .with_autogen_warning("/* 自动生成的头文件，请勿手动修改 */")
        .with_documentation(true)
        .with_cpp_compat(true)
        .generate()
        .expect("无法生成C头文件")
        .write_to_file(output_dir.join(format!("{}.h", package_name)));

    println!("cargo:rerun-if-changed=src/");
    println!("cargo:rerun-if-changed=Cargo.toml");
}

fn target_dir() -> PathBuf {
    if let Ok(target) = env::var("CARGO_TARGET_DIR") {
        PathBuf::from(target)
    } else {
        PathBuf::from(env::var("CARGO_MANIFEST_DIR").unwrap()).join("target")
    }
}
```

---

## 📁 Rust项目结构

### **推荐目录结构**
```
rust_core/
├── Cargo.toml              # 项目配置
├── build.rs                # 构建脚本
├── cbindgen.toml           # C绑定配置
├── src/
│   ├── lib.rs              # 库入口
│   ├── ffi/
│   │   ├── mod.rs          # FFI模块
│   │   ├── tts.rs          # TTS FFI接口
│   │   ├── ocr.rs          # OCR FFI接口
│   │   ├── pdf.rs          # PDF FFI接口
│   │   ├── sync.rs         # 同步FFI接口
│   │   └── cache.rs        # 缓存FFI接口
│   ├── core/
│   │   ├── mod.rs          # 核心模块
│   │   ├── tts/            # TTS引擎
│   │   ├── ocr/            # OCR引擎
│   │   ├── pdf/            # PDF引擎
│   │   ├── sync/           # 同步引擎
│   │   └── cache/          # 缓存引擎
│   ├── utils/
│   │   ├── mod.rs          # 工具模块
│   │   ├── error.rs        # 错误处理
│   │   ├── logger.rs       # 日志工具
│   │   └── memory.rs       # 内存管理
│   └── tests/
│       ├── integration/    # 集成测试
│       └── unit/           # 单元测试
├── target/                 # 构建输出
└── examples/               # 示例代码
```

---

## 🔨 构建命令

### **开发构建**
```bash
# 进入Rust项目目录
cd rust_core

# 开发构建 (调试版本)
cargo build

# 发布构建 (优化版本)
cargo build --release

# 清理构建
cargo clean

# 运行测试
cargo test

# 生成文档
cargo doc --open
```

### **跨平台构建**
```bash
# Windows构建
cargo build --release --target x86_64-pc-windows-msvc

# Linux构建
cargo build --release --target x86_64-unknown-linux-gnu

# macOS构建
cargo build --release --target x86_64-apple-darwin
```

---

## 📋 cbindgen配置

### **cbindgen.toml配置文件**
```toml
# rust_core/cbindgen.toml
language = "C"
autogen_warning = "/* 自动生成的头文件，请勿手动修改 */"
include_version = true
namespace = "PDFReaderCore"
tab_width = 2
line_length = 100
documentation = true
documentation_style = "doxy"

[export]
include = [
    "FFIResult",
    "TTSConfig",
    "OCRConfig", 
    "PDFConfig",
    "SyncConfig",
    "CacheConfig"
]

[export.rename]
"FFIResult" = "PDFReaderResult"

[parse]
parse_deps = true
include = ["pdf_reader_core"]

[macro_expansion]
bitflags = true
```

---

## 🚀 FFI接口示例

### **lib.rs主入口**
```rust
// rust_core/src/lib.rs
use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_int};

pub mod core;
pub mod ffi;
pub mod utils;

// 导出FFI函数
pub use ffi::*;

/// 初始化PDF阅读器核心库
#[no_mangle]
pub extern "C" fn pdf_reader_init() -> c_int {
    utils::logger::init();
    log::info!("PDF阅读器核心库初始化成功");
    0 // 成功返回0
}

/// 清理PDF阅读器核心库
#[no_mangle]
pub extern "C" fn pdf_reader_cleanup() -> c_int {
    log::info!("PDF阅读器核心库清理完成");
    0 // 成功返回0
}

/// 获取库版本信息
#[no_mangle]
pub extern "C" fn pdf_reader_version() -> *const c_char {
    let version = CString::new(env!("CARGO_PKG_VERSION")).unwrap();
    version.into_raw()
}
```

### **TTS FFI接口示例**
```rust
// rust_core/src/ffi/tts.rs
use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_int};

/// TTS合成并播放
#[no_mangle]
pub extern "C" fn tts_synthesize_and_play(
    text: *const c_char,
    text_len: c_int,
) -> c_int {
    if text.is_null() {
        return -1; // 参数错误
    }

    let text_str = unsafe {
        CStr::from_ptr(text).to_string_lossy()
    };

    match crate::core::tts::synthesize_and_play(&text_str) {
        Ok(_) => 0,  // 成功
        Err(_) => -1, // 失败
    }
}

/// TTS暂停播放
#[no_mangle]
pub extern "C" fn tts_pause() -> c_int {
    match crate::core::tts::pause() {
        Ok(_) => 0,
        Err(_) => -1,
    }
}

/// TTS恢复播放
#[no_mangle]
pub extern "C" fn tts_resume() -> c_int {
    match crate::core::tts::resume() {
        Ok(_) => 0,
        Err(_) => -1,
    }
}

/// TTS停止播放
#[no_mangle]
pub extern "C" fn tts_stop() -> c_int {
    match crate::core::tts::stop() {
        Ok(_) => 0,
        Err(_) => -1,
    }
}
```

---

## 🔧 Flutter集成配置

### **Flutter端动态库加载**
```dart
// lib/core/ffi/library_loader.dart
import 'dart:ffi';
import 'dart:io';

class LibraryLoader {
  static DynamicLibrary? _library;

  static DynamicLibrary get library {
    if (_library != null) return _library!;

    const libraryName = 'pdf_reader_core';
    
    if (Platform.isWindows) {
      _library = DynamicLibrary.open('$libraryName.dll');
    } else if (Platform.isLinux) {
      _library = DynamicLibrary.open('lib$libraryName.so');
    } else if (Platform.isMacOS) {
      _library = DynamicLibrary.open('lib$libraryName.dylib');
    } else {
      throw UnsupportedError('不支持的平台: ${Platform.operatingSystem}');
    }

    return _library!;
  }

  static void dispose() {
    _library = null;
  }
}
```

---

## 📦 部署配置

### **动态库部署位置**
```
flutter_app_new/
├── windows/
│   └── runner/
│       └── pdf_reader_core.dll    # Windows动态库
├── linux/
│   └── lib/
│       └── libpdf_reader_core.so  # Linux动态库
├── macos/
│   └── Runner/
│       └── libpdf_reader_core.dylib # macOS动态库
└── lib/
    └── core/
        └── ffi/                    # FFI接口代码
```

### **自动化构建脚本**
```bash
#!/bin/bash
# build_rust_library.sh

echo "🚀 开始构建Rust动态库..."

# 进入Rust项目目录
cd rust_core

# 构建发布版本
cargo build --release

# 复制动态库到Flutter项目
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    cp target/release/pdf_reader_core.dll ../windows/runner/
    echo "✅ Windows动态库构建完成"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    cp target/release/libpdf_reader_core.so ../linux/lib/
    echo "✅ Linux动态库构建完成"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    cp target/release/libpdf_reader_core.dylib ../macos/Runner/
    echo "✅ macOS动态库构建完成"
fi

echo "🎉 Rust动态库构建和部署完成！"
```

---

## 🧪 测试配置

### **Rust单元测试**
```rust
// rust_core/src/tests/unit/tts_test.rs
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_tts_synthesize() {
        let result = crate::core::tts::synthesize("测试文本");
        assert!(result.is_ok());
    }

    #[test]
    fn test_tts_ffi_interface() {
        use std::ffi::CString;
        
        let text = CString::new("测试文本").unwrap();
        let result = crate::ffi::tts::tts_synthesize_and_play(
            text.as_ptr(),
            text.as_bytes().len() as i32,
        );
        assert_eq!(result, 0); // 成功返回0
    }
}
```

### **集成测试**
```rust
// rust_core/tests/integration_test.rs
use pdf_reader_core::*;

#[test]
fn test_library_initialization() {
    let result = pdf_reader_init();
    assert_eq!(result, 0);
    
    let result = pdf_reader_cleanup();
    assert_eq!(result, 0);
}
```

---

## 📊 构建验证清单

### **构建成功验证**
- [ ] Cargo构建无错误
- [ ] 动态库文件生成成功
- [ ] C头文件生成正确
- [ ] FFI接口导出完整
- [ ] 单元测试全部通过
- [ ] 集成测试验证成功
- [ ] Flutter端能正确加载动态库
- [ ] FFI调用功能正常

### **性能验证**
- [ ] 发布版本优化生效
- [ ] 动态库大小合理 (<50MB)
- [ ] 加载时间可接受 (<1秒)
- [ ] 内存使用稳定
- [ ] CPU使用率正常

---

**构建配置完成时间**: 2025-07-18  
**配置作者**: Augment Agent  
**适用版本**: PDF阅读器 v2.0+  
**下一步**: 执行构建脚本，验证FFI调用
