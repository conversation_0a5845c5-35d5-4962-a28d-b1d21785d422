/// 🚀 FFI模块 (src/ffi/mod.rs)
///
/// 模块组织:
/// ✅ TTS FFI接口 (tts.rs) - 语音合成和播放接口
/// ✅ OCR FFI接口 (ocr.rs) - 文字识别接口
/// ✅ PDF FFI接口 (pdf.rs) - PDF处理接口
/// ✅ 同步FFI接口 (sync.rs) - 数据同步接口
/// ✅ 缓存FFI接口 (cache.rs) - 缓存管理接口
///
/// FFI设计原则:
/// - C兼容的函数签名
/// - 统一的错误处理
/// - 内存安全管理
/// - 线程安全保证
/// - 性能优化
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-18
/// 最后更新: 2025-07-18 (🚀 FFI模块版本)

// 子模块声明
pub mod tts;
pub mod ocr;
pub mod pdf;
pub mod sync;
pub mod cache;

// 重新导出所有FFI函数
pub use tts::*;
pub use ocr::*;
pub use pdf::*;
pub use sync::*;
pub use cache::*;

use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_int};
use std::ptr;

/// FFI错误码定义
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FFIErrorCode {
    Success = 0,
    InvalidParameter = -1,
    InternalError = -2,
    NotInitialized = -3,
    OutOfMemory = -4,
    FileNotFound = -5,
    PermissionDenied = -6,
    NetworkError = -7,
    TimeoutError = -8,
    UnsupportedOperation = -9,
    Unknown = -999,
}

impl From<crate::utils::error::Error> for FFIErrorCode {
    fn from(error: crate::utils::error::Error) -> Self {
        match error {
            crate::utils::error::Error::InvalidParameter(_) => FFIErrorCode::InvalidParameter,
            crate::utils::error::Error::FileNotFound(_) => FFIErrorCode::FileNotFound,
            crate::utils::error::Error::PermissionDenied(_) => FFIErrorCode::PermissionDenied,
            crate::utils::error::Error::NetworkError(_) => FFIErrorCode::NetworkError,
            crate::utils::error::Error::TimeoutError(_) => FFIErrorCode::TimeoutError,
            _ => FFIErrorCode::InternalError,
        }
    }
}

/// FFI结果类型
#[repr(C)]
pub struct FFIResult {
    pub error_code: FFIErrorCode,
    pub data: *const c_char,
    pub error_message: *const c_char,
}

impl FFIResult {
    /// 创建成功结果
    pub fn success(data: Option<String>) -> Self {
        let data_ptr = if let Some(data_str) = data {
            match CString::new(data_str) {
                Ok(c_string) => c_string.into_raw(),
                Err(_) => ptr::null(),
            }
        } else {
            ptr::null()
        };

        FFIResult {
            error_code: FFIErrorCode::Success,
            data: data_ptr,
            error_message: ptr::null(),
        }
    }

    /// 创建错误结果
    pub fn error(error_code: FFIErrorCode, message: String) -> Self {
        let error_message_ptr = match CString::new(message) {
            Ok(c_string) => c_string.into_raw(),
            Err(_) => ptr::null(),
        };

        FFIResult {
            error_code,
            data: ptr::null(),
            error_message: error_message_ptr,
        }
    }
}

/// 释放FFI结果
#[no_mangle]
pub extern "C" fn free_ffi_result(result: FFIResult) {
    if !result.data.is_null() {
        unsafe {
            let _ = CString::from_raw(result.data as *mut c_char);
        }
    }
    if !result.error_message.is_null() {
        unsafe {
            let _ = CString::from_raw(result.error_message as *mut c_char);
        }
    }
}

/// 辅助函数：安全地从C字符串创建Rust字符串
pub(crate) fn safe_c_str_to_string(ptr: *const c_char) -> Result<String, FFIErrorCode> {
    if ptr.is_null() {
        return Err(FFIErrorCode::InvalidParameter);
    }

    unsafe {
        CStr::from_ptr(ptr)
            .to_str()
            .map(|s| s.to_string())
            .map_err(|_| FFIErrorCode::InvalidParameter)
    }
}

/// 辅助函数：安全地创建C字符串
pub(crate) fn safe_string_to_c_str(s: String) -> *const c_char {
    match CString::new(s) {
        Ok(c_string) => c_string.into_raw(),
        Err(_) => ptr::null(),
    }
}

/// 辅助函数：记录FFI调用
pub(crate) fn log_ffi_call(function_name: &str, params: &str) {
    log::debug!("🔗 FFI调用: {} (参数: {})", function_name, params);
}

/// 辅助函数：记录FFI错误
pub(crate) fn log_ffi_error(function_name: &str, error: &str) {
    log::error!("❌ FFI错误 {}: {}", function_name, error);
}

/// 辅助函数：记录FFI成功
pub(crate) fn log_ffi_success(function_name: &str, result: &str) {
    log::debug!("✅ FFI成功 {}: {}", function_name, result);
}

/// 获取最后的错误信息
static mut LAST_ERROR: Option<String> = None;

/// 设置最后的错误信息
pub(crate) fn set_last_error(error: String) {
    unsafe {
        LAST_ERROR = Some(error);
    }
}

/// 获取最后的错误信息
#[no_mangle]
pub extern "C" fn get_last_error() -> *const c_char {
    unsafe {
        match &LAST_ERROR {
            Some(error) => safe_string_to_c_str(error.clone()),
            None => ptr::null(),
        }
    }
}

/// 清除最后的错误信息
#[no_mangle]
pub extern "C" fn clear_last_error() {
    unsafe {
        LAST_ERROR = None;
    }
}

/// FFI性能统计
#[derive(Debug, Default)]
pub struct FFIStats {
    pub total_calls: u64,
    pub successful_calls: u64,
    pub failed_calls: u64,
    pub total_time_ms: u64,
}

static mut FFI_STATS: FFIStats = FFIStats {
    total_calls: 0,
    successful_calls: 0,
    failed_calls: 0,
    total_time_ms: 0,
};

/// 记录FFI调用统计
pub(crate) fn record_ffi_call(success: bool, duration_ms: u64) {
    unsafe {
        FFI_STATS.total_calls += 1;
        FFI_STATS.total_time_ms += duration_ms;
        
        if success {
            FFI_STATS.successful_calls += 1;
        } else {
            FFI_STATS.failed_calls += 1;
        }
    }
}

/// 获取FFI统计信息
#[no_mangle]
pub extern "C" fn get_ffi_stats() -> *const c_char {
    unsafe {
        let stats_json = serde_json::json!({
            "total_calls": FFI_STATS.total_calls,
            "successful_calls": FFI_STATS.successful_calls,
            "failed_calls": FFI_STATS.failed_calls,
            "total_time_ms": FFI_STATS.total_time_ms,
            "average_time_ms": if FFI_STATS.total_calls > 0 {
                FFI_STATS.total_time_ms as f64 / FFI_STATS.total_calls as f64
            } else {
                0.0
            },
            "success_rate": if FFI_STATS.total_calls > 0 {
                FFI_STATS.successful_calls as f64 / FFI_STATS.total_calls as f64
            } else {
                0.0
            }
        });

        safe_string_to_c_str(stats_json.to_string())
    }
}

/// 重置FFI统计信息
#[no_mangle]
pub extern "C" fn reset_ffi_stats() {
    unsafe {
        FFI_STATS = FFIStats::default();
    }
}

/// FFI调用计时器
pub struct FFITimer {
    start_time: std::time::Instant,
}

impl FFITimer {
    pub fn new() -> Self {
        FFITimer {
            start_time: std::time::Instant::now(),
        }
    }

    pub fn elapsed_ms(&self) -> u64 {
        self.start_time.elapsed().as_millis() as u64
    }
}

/// FFI调用宏，用于统一处理调用统计和错误记录
#[macro_export]
macro_rules! ffi_call {
    ($func_name:expr, $body:expr) => {{
        let timer = $crate::ffi::FFITimer::new();
        $crate::ffi::log_ffi_call($func_name, "");
        
        let result = $body;
        let duration = timer.elapsed_ms();
        
        match &result {
            Ok(_) => {
                $crate::ffi::record_ffi_call(true, duration);
                $crate::ffi::log_ffi_success($func_name, "调用成功");
            }
            Err(e) => {
                $crate::ffi::record_ffi_call(false, duration);
                let error_msg = format!("{:?}", e);
                $crate::ffi::set_last_error(error_msg.clone());
                $crate::ffi::log_ffi_error($func_name, &error_msg);
            }
        }
        
        result
    }};
}

// 测试模块
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ffi_result_success() {
        let result = FFIResult::success(Some("test data".to_string()));
        assert_eq!(result.error_code, FFIErrorCode::Success);
        assert!(!result.data.is_null());
        assert!(result.error_message.is_null());

        // 清理内存
        free_ffi_result(result);
    }

    #[test]
    fn test_ffi_result_error() {
        let result = FFIResult::error(FFIErrorCode::InvalidParameter, "test error".to_string());
        assert_eq!(result.error_code, FFIErrorCode::InvalidParameter);
        assert!(result.data.is_null());
        assert!(!result.error_message.is_null());

        // 清理内存
        free_ffi_result(result);
    }

    #[test]
    fn test_safe_c_str_conversion() {
        let test_str = "test string";
        let c_str = std::ffi::CString::new(test_str).unwrap();
        let c_ptr = c_str.as_ptr();

        let result = safe_c_str_to_string(c_ptr);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), test_str);
    }

    #[test]
    fn test_ffi_stats() {
        reset_ffi_stats();
        
        record_ffi_call(true, 100);
        record_ffi_call(false, 200);
        
        let stats_ptr = get_ffi_stats();
        assert!(!stats_ptr.is_null());
        
        let stats_str = unsafe {
            CStr::from_ptr(stats_ptr).to_string_lossy()
        };
        
        let stats: serde_json::Value = serde_json::from_str(&stats_str).unwrap();
        assert_eq!(stats["total_calls"], 2);
        assert_eq!(stats["successful_calls"], 1);
        assert_eq!(stats["failed_calls"], 1);
        
        // 清理内存
        crate::free_ffi_string(stats_ptr as *mut c_char);
    }
}
