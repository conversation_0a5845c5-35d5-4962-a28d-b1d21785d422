/// 🚀 核心模块 (src/core/mod.rs)
///
/// 模块组织:
/// ✅ TTS核心引擎 (tts.rs) - 语音合成核心逻辑
/// ✅ OCR核心引擎 (ocr.rs) - 文字识别核心逻辑
/// ✅ PDF核心引擎 (pdf.rs) - PDF处理核心逻辑
/// ✅ 同步核心引擎 (sync.rs) - 数据同步核心逻辑
/// ✅ 缓存核心引擎 (cache.rs) - 缓存管理核心逻辑
///
/// 核心设计原则:
/// - 高性能: 优化算法和数据结构
/// - 线程安全: 支持多线程并发访问
/// - 内存安全: 严格的内存管理
/// - 错误处理: 完善的错误处理机制
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-18
/// 最后更新: 2025-07-18 (🚀 核心模块版本)

// 子模块声明
pub mod tts;
pub mod ocr;
pub mod pdf;
pub mod sync;
pub mod cache;

use crate::utils::error::Result;
use std::sync::Once;

/// 全局初始化标志
static INIT: Once = Once::new();

/// 初始化核心组件
pub fn init() -> Result<()> {
    INIT.call_once(|| {
        log::info!("🚀 初始化PDF阅读器核心组件...");
        
        // 初始化各个核心模块
        if let Err(e) = tts::init() {
            log::error!("❌ TTS模块初始化失败: {}", e);
        } else {
            log::info!("✅ TTS模块初始化成功");
        }
        
        if let Err(e) = ocr::init() {
            log::error!("❌ OCR模块初始化失败: {}", e);
        } else {
            log::info!("✅ OCR模块初始化成功");
        }
        
        if let Err(e) = pdf::init() {
            log::error!("❌ PDF模块初始化失败: {}", e);
        } else {
            log::info!("✅ PDF模块初始化成功");
        }
        
        if let Err(e) = sync::init() {
            log::error!("❌ 同步模块初始化失败: {}", e);
        } else {
            log::info!("✅ 同步模块初始化成功");
        }
        
        if let Err(e) = cache::init() {
            log::error!("❌ 缓存模块初始化失败: {}", e);
        } else {
            log::info!("✅ 缓存模块初始化成功");
        }
        
        log::info!("🎉 PDF阅读器核心组件初始化完成");
    });
    
    Ok(())
}

/// 清理核心组件
pub fn cleanup() -> Result<()> {
    log::info!("🧹 开始清理PDF阅读器核心组件...");
    
    // 清理各个核心模块
    if let Err(e) = cache::cleanup() {
        log::error!("❌ 缓存模块清理失败: {}", e);
    } else {
        log::info!("✅ 缓存模块清理成功");
    }
    
    if let Err(e) = sync::cleanup() {
        log::error!("❌ 同步模块清理失败: {}", e);
    } else {
        log::info!("✅ 同步模块清理成功");
    }
    
    if let Err(e) = pdf::cleanup() {
        log::error!("❌ PDF模块清理失败: {}", e);
    } else {
        log::info!("✅ PDF模块清理成功");
    }
    
    if let Err(e) = ocr::cleanup() {
        log::error!("❌ OCR模块清理失败: {}", e);
    } else {
        log::info!("✅ OCR模块清理成功");
    }
    
    if let Err(e) = tts::cleanup() {
        log::error!("❌ TTS模块清理失败: {}", e);
    } else {
        log::info!("✅ TTS模块清理成功");
    }
    
    log::info!("🎉 PDF阅读器核心组件清理完成");
    Ok(())
}

/// 获取核心组件状态
pub fn get_status() -> serde_json::Value {
    serde_json::json!({
        "tts": tts::get_status(),
        "ocr": ocr::get_status(),
        "pdf": pdf::get_status(),
        "sync": sync::get_status(),
        "cache": cache::get_status(),
    })
}

/// 运行核心组件自检
pub fn self_test() -> Result<()> {
    log::info!("🧪 开始核心组件自检...");
    
    let mut failed_tests = 0;
    
    // 测试TTS模块
    if let Err(e) = tts::self_test() {
        log::error!("❌ TTS模块自检失败: {}", e);
        failed_tests += 1;
    } else {
        log::info!("✅ TTS模块自检通过");
    }
    
    // 测试OCR模块
    if let Err(e) = ocr::self_test() {
        log::error!("❌ OCR模块自检失败: {}", e);
        failed_tests += 1;
    } else {
        log::info!("✅ OCR模块自检通过");
    }
    
    // 测试PDF模块
    if let Err(e) = pdf::self_test() {
        log::error!("❌ PDF模块自检失败: {}", e);
        failed_tests += 1;
    } else {
        log::info!("✅ PDF模块自检通过");
    }
    
    // 测试同步模块
    if let Err(e) = sync::self_test() {
        log::error!("❌ 同步模块自检失败: {}", e);
        failed_tests += 1;
    } else {
        log::info!("✅ 同步模块自检通过");
    }
    
    // 测试缓存模块
    if let Err(e) = cache::self_test() {
        log::error!("❌ 缓存模块自检失败: {}", e);
        failed_tests += 1;
    } else {
        log::info!("✅ 缓存模块自检通过");
    }
    
    if failed_tests == 0 {
        log::info!("🎉 所有核心组件自检通过！");
        Ok(())
    } else {
        Err(crate::utils::error::Error::InternalError(
            format!("{} 个核心组件自检失败", failed_tests)
        ))
    }
}

// 测试模块
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_core_initialization() {
        let result = init();
        assert!(result.is_ok(), "核心组件初始化应该成功");

        let result = cleanup();
        assert!(result.is_ok(), "核心组件清理应该成功");
    }

    #[test]
    fn test_core_status() {
        init().unwrap();

        let status = get_status();
        assert!(status.is_object(), "状态信息应该是对象");
        assert!(status["tts"].is_object(), "应该包含TTS状态");
        assert!(status["ocr"].is_object(), "应该包含OCR状态");
        assert!(status["pdf"].is_object(), "应该包含PDF状态");
        assert!(status["sync"].is_object(), "应该包含同步状态");
        assert!(status["cache"].is_object(), "应该包含缓存状态");

        cleanup().unwrap();
    }

    #[test]
    fn test_core_self_test() {
        init().unwrap();

        let result = self_test();
        // 注意：在测试环境中，某些模块可能会失败，这是正常的
        assert!(result.is_ok() || result.is_err(), "自检应该返回有效结果");

        cleanup().unwrap();
    }
}
