/// 🚀 工具模块 (src/utils/mod.rs)
///
/// 模块组织:
/// ✅ 错误处理 (error.rs) - 统一的错误处理系统
/// ✅ 日志工具 (logger.rs) - 日志系统配置和管理
/// ✅ 内存管理 (memory.rs) - 内存使用监控和管理
/// ✅ 时间工具 (time.rs) - 时间相关的工具函数
/// ✅ 全局状态 (state.rs) - 全局状态管理
///
/// 工具设计原则:
/// - 通用性: 可在各个模块中复用
/// - 高效性: 优化的实现和最小开销
/// - 安全性: 线程安全和内存安全
/// - 易用性: 简洁的API和清晰的文档
///
/// 作者: Augment Agent
/// 创建时间: 2025-07-18
/// 最后更新: 2025-07-18 (🚀 工具模块版本)

// 子模块声明
pub mod error;
pub mod logger;
pub mod memory;
pub mod time;
pub mod state;

// 重新导出常用类型
pub use error::{Error, Result};
pub use logger::init as init_logger;
pub use memory::get_usage_info;
pub use time::get_uptime_seconds;
pub use state::get_global_state;
