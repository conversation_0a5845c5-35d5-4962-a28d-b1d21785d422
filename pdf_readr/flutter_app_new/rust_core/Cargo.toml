[package]
name = "pdf_reader_core"
version = "0.1.0"
edition = "2021"
authors = ["Augment Agent <<EMAIL>>"]
description = "PDF阅读器核心库 - Rust FFI实现"
license = "MIT"
repository = "https://github.com/augment/pdf_reader_core"

[lib]
name = "pdf_reader_core"
crate-type = ["cdylib", "rlib"]  # 生成C兼容的动态库和Rust库

[dependencies]
# 基础依赖
libc = "0.2"
once_cell = "1.19"

# 异步处理
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
log = "0.4"
env_logger = "0.10"

# 缓存
lru = "0.12"

# 同步
parking_lot = "0.12"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# PDF处理 (可选，用于真实实现)
# lopdf = { version = "0.26.0", optional = true }
# pdf-extract = { version = "0.6.4", optional = true }

# OCR处理 (可选，用于真实实现)
# tesseract = { version = "0.13.0", optional = true }
# image = { version = "0.24.0", optional = true }

# TTS处理 (可选，用于真实实现)
# tts = { version = "0.25.0", optional = true }
# rodio = { version = "0.17.0", optional = true }

# 数据库 (可选，用于真实实现)
# rusqlite = { version = "0.29.0", features = ["bundled"], optional = true }

[build-dependencies]
cbindgen = "0.24"

[features]
default = ["mock"]
mock = []  # 模拟实现
full = [
    # "lopdf", "pdf-extract",    # PDF功能
    # "tesseract", "image",      # OCR功能
    # "tts", "rodio",            # TTS功能
    # "rusqlite",                # 数据库功能
]

[profile.dev]
opt-level = 0
debug = true
overflow-checks = true

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.release-with-debug]
inherits = "release"
debug = true
strip = false

# 平台特定配置
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "processthreadsapi"] }

[target.'cfg(unix)'.dependencies]
nix = "0.27"

# 文档配置
[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

# 发布配置
[package.metadata.release]
pre-release-replacements = [
  {file="CHANGELOG.md", search="Unreleased", replace="{{version}}"},
  {file="CHANGELOG.md", search="\\.\\.\\.HEAD", replace="...{{tag_name}}", exactly=1},
  {file="CHANGELOG.md", search="ReleaseDate", replace="{{date}}"},
  {file="CHANGELOG.md", search="<!-- next-header -->", replace="<!-- next-header -->\n\n## [Unreleased] - ReleaseDate", exactly=1},
  {file="CHANGELOG.md", search="<!-- next-url -->", replace="<!-- next-url -->\n[Unreleased]: https://github.com/augment/pdf_reader_core/compare/{{tag_name}}...HEAD", exactly=1},
]
