@echo off
REM Tesseract OCR Windows 自动安装脚本
REM 注意：此脚本需要管理员权限

echo ========================================
echo    Tesseract OCR 自动安装脚本
echo ========================================
echo.

REM 检查是否有管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 检测到管理员权限
) else (
    echo ❌ 需要管理员权限运行此脚本
    echo 💡 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 🔍 检查 Chocolatey 是否已安装...
choco --version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Chocolatey 已安装
    goto install_tesseract
) else (
    echo ⚠️  Chocolatey 未安装
    echo 💡 正在安装 Chocolatey...
    
    REM 安装 Chocolatey
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    
    if %errorLevel% == 0 (
        echo ✅ Chocolatey 安装成功
    ) else (
        echo ❌ Chocolatey 安装失败
        goto manual_install
    )
)

:install_tesseract
echo.
echo 📦 正在安装 Tesseract OCR...
choco install tesseract -y

if %errorLevel% == 0 (
    echo ✅ Tesseract 安装成功
) else (
    echo ❌ Tesseract 安装失败
    goto manual_install
)

echo.
echo 🌍 正在安装语言包...
choco install tesseract-languages -y

echo.
echo 🔧 正在验证安装...
tesseract --version
if %errorLevel% == 0 (
    echo ✅ Tesseract 验证成功
    goto success
) else (
    echo ⚠️  Tesseract 验证失败，可能需要重启命令行
    goto success
)

:manual_install
echo.
echo ========================================
echo        手动安装说明
echo ========================================
echo.
echo 自动安装失败，请手动安装：
echo.
echo 1. 访问: https://github.com/UB-Mannheim/tesseract/wiki
echo 2. 下载最新版本的 tesseract-ocr-w64-setup-v5.x.x.exe
echo 3. 运行安装程序
echo 4. 在安装过程中选择语言包：
echo    - English (eng)
echo    - Chinese Simplified (chi_sim)
echo    - Chinese Traditional (chi_tra)
echo 5. 将安装路径添加到系统 PATH 环境变量
echo    通常是: C:\Program Files\Tesseract-OCR
echo.
goto end

:success
echo.
echo ========================================
echo        安装完成！
echo ========================================
echo.
echo ✅ Tesseract OCR 安装成功
echo.
echo 📋 下一步：
echo 1. 重启命令行或 IDE
echo 2. 运行测试: cargo test --features tesseract
echo 3. 验证安装: tesseract --version
echo.
echo 🌍 可用语言检查: tesseract --list-langs
echo.

:end
echo 按任意键退出...
pause >nul
