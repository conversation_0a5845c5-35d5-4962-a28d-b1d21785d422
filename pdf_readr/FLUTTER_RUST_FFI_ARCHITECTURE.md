# 🏗️ Flutter + Rust FFI 架构设计文档

## 📋 架构概述

**核心理念**: 用户操作 → 前端UI → 直接FFI调用 → 后端统一处理 → 事件通知 → UI更新

### **架构优势**
- ✅ **性能优化**: Rust处理计算密集型任务 (OCR、数据库、AI模型)
- ✅ **内存安全**: Rust的内存安全保证，避免崩溃和内存泄漏
- ✅ **跨平台一致性**: Flutter确保UI在所有平台的一致性
- ✅ **并发处理**: Rust的异步处理能力，支持高并发操作
- ✅ **模块化设计**: 清晰的职责分离，易于维护和扩展

---

## 🔧 技术栈详细配置

### **Rust后端技术栈**
```toml
[dependencies]
# 核心异步运行时
tokio = { version = "1.0", features = ["full"] }

# 数据库操作
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# FFI桥接
flutter_rust_bridge = "1.82"

# 序列化/反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 图像处理
image = "0.24"

# OCR引擎 (条件编译)
tesseract = { version = "0.13", optional = true }

# 加密
aes-gcm = "0.10"
sha2 = "0.10"

# 日志
log = "0.4"
env_logger = "0.10"

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID生成
uuid = { version = "1.0", features = ["v4", "serde"] }

[features]
default = []
tesseract = ["dep:tesseract"]
```

### **Flutter前端技术栈**
```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # FFI集成
  flutter_rust_bridge: ^1.82.0
  
  # 状态管理
  flutter_riverpod: ^2.4.0
  
  # 路由管理
  go_router: ^10.0.0
  
  # UI组件
  material_design_icons_flutter: ^7.0.0
  
  # 文件操作
  path_provider: ^2.1.0
  file_picker: ^5.3.0
  
  # 权限管理
  permission_handler: ^10.4.0
  
  # 国际化
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.0
  
  # 性能监控
  flutter_performance_monitor: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.4.0
```

---

## 🔄 FFI通信机制

### **1. 统一FFI服务接口**
```dart
// Flutter端统一FFI服务
class AdvancedFFIService {
  static const MethodChannel _channel = MethodChannel('rust_core');
  
  // 统一的Rust函数调用接口
  Future<Map<String, dynamic>> callRustFunction(
    String functionName, 
    Map<String, dynamic> parameters
  ) async {
    try {
      final result = await _channel.invokeMethod('call_rust_function', {
        'function_name': functionName,
        'parameters': parameters,
      });
      
      return Map<String, dynamic>.from(result);
    } catch (e) {
      throw FFIException('FFI调用失败: $functionName', e);
    }
  }
  
  // 事件流监听
  Stream<Map<String, dynamic>> get eventStream {
    return EventChannel('rust_core_events')
        .receiveBroadcastStream()
        .map((event) => Map<String, dynamic>.from(event));
  }
}
```

### **2. Rust端FFI处理器**
```rust
// Rust端统一FFI处理
#[flutter_rust_bridge::frb(sync)]
pub fn call_rust_function(function_name: String, parameters: String) -> String {
    let params: serde_json::Value = serde_json::from_str(&parameters)
        .unwrap_or_else(|_| serde_json::Value::Null);
    
    let result = match function_name.as_str() {
        // PDF处理
        "pdf_parse_document" => handle_pdf_parse(params),
        "pdf_render_page" => handle_pdf_render(params),
        "pdf_extract_text" => handle_pdf_extract_text(params),
        
        // OCR处理
        "ocr_process_image" => handle_ocr_process(params),
        "ocr_batch_process" => handle_ocr_batch_process(params),
        
        // 数据库操作
        "db_save_ocr_result" => handle_db_save_ocr(params),
        "db_get_document_info" => handle_db_get_document(params),
        "db_create_version" => handle_db_create_version(params),
        
        // TTS功能
        "tts_synthesize_and_play" => handle_tts_synthesize(params),
        "tts_set_voice_parameters" => handle_tts_set_voice(params),
        
        // 格式转换
        "format_convert_document" => handle_format_convert(params),
        "format_export_document" => handle_format_export(params),
        
        _ => {
            log::error!("未知的FFI函数: {}", function_name);
            serde_json::json!({
                "success": false,
                "error": format!("未知函数: {}", function_name)
            })
        }
    };
    
    serde_json::to_string(&result).unwrap_or_else(|_| "{}".to_string())
}
```

### **3. 异步事件通知机制**
```rust
// Rust端事件发送器
pub struct EventNotifier {
    sender: tokio::sync::broadcast::Sender<AppEvent>,
}

impl EventNotifier {
    pub fn notify_ocr_progress(&self, document_id: &str, page: usize, progress: f32) {
        let event = AppEvent::OcrProgress {
            document_id: document_id.to_string(),
            page,
            progress,
            timestamp: chrono::Utc::now(),
        };
        
        let _ = self.sender.send(event);
    }
    
    pub fn notify_tts_state_change(&self, state: TTSState) {
        let event = AppEvent::TTSStateChange {
            state,
            timestamp: chrono::Utc::now(),
        };
        
        let _ = self.sender.send(event);
    }
}

#[derive(Debug, Clone, Serialize)]
pub enum AppEvent {
    OcrProgress {
        document_id: String,
        page: usize,
        progress: f32,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    TTSStateChange {
        state: TTSState,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    DatabaseUpdate {
        table: String,
        operation: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
}
```

---

## 📊 数据流架构

### **数据流向图**
```
用户操作 → Flutter UI → FFI调用 → Rust处理 → 数据库存储
    ↑                                              ↓
UI更新 ← 事件通知 ← 异步处理 ← 后台任务 ← 队列调度
```

### **核心数据流**

#### **1. PDF文档处理流程**
```
1. 用户选择PDF文件
   ↓
2. Flutter调用 pdf_parse_document
   ↓
3. Rust解析PDF结构和元数据
   ↓
4. 检测是否为扫描版PDF
   ↓
5. 如果是扫描版，启动OCR处理队列
   ↓
6. 将结果存储到SQLite数据库
   ↓
7. 通过事件流通知Flutter更新UI
```

#### **2. OCR处理流程**
```
1. 后台OCR队列接收任务
   ↓
2. 图像预处理和优化
   ↓
3. Tesseract OCR文字识别
   ↓
4. 结果后处理和置信度评估
   ↓
5. 存储到版本控制数据库
   ↓
6. 发送进度事件到Flutter
   ↓
7. UI实时更新处理进度
```

#### **3. 实时编辑流程**
```
1. 用户在重排视图中编辑文本
   ↓
2. Flutter防抖处理，避免频繁调用
   ↓
3. 调用 db_save_text_edit 保存修改
   ↓
4. Rust创建新的文本版本
   ↓
5. 更新版本控制历史
   ↓
6. 通知其他组件数据变更
```

---

## 🔒 安全和性能考虑

### **内存安全**
- ✅ **Rust内存安全**: 编译时内存安全保证
- ✅ **FFI边界检查**: 严格的数据类型验证
- ✅ **资源自动释放**: RAII模式确保资源正确释放

### **数据安全**
- ✅ **AES-256加密**: 数据库内容加密存储
- ✅ **安全传输**: FFI调用数据验证和清理
- ✅ **权限控制**: 文件访问权限严格控制

### **性能优化**
- ✅ **异步处理**: 所有耗时操作异步执行
- ✅ **内存池**: 对象复用减少内存分配
- ✅ **智能缓存**: 基于LRU的智能缓存策略
- ✅ **批量操作**: 数据库批量操作优化

---

## 🧪 测试策略

### **Rust后端测试**
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_ffi_pdf_parse() {
        let params = serde_json::json!({
            "file_path": "test.pdf"
        });
        
        let result = handle_pdf_parse(params);
        assert!(result["success"].as_bool().unwrap());
    }
    
    #[tokio::test]
    async fn test_ocr_processing() {
        let params = serde_json::json!({
            "image_data": "base64_encoded_image",
            "language": "eng+chi_sim"
        });
        
        let result = handle_ocr_process(params);
        assert!(result["text"].as_str().is_some());
    }
}
```

### **Flutter前端测试**
```dart
void main() {
  group('FFI Service Tests', () {
    late AdvancedFFIService ffiService;
    
    setUp(() {
      ffiService = AdvancedFFIService();
    });
    
    testWidgets('PDF解析测试', (WidgetTester tester) async {
      final result = await ffiService.callRustFunction('pdf_parse_document', {
        'file_path': 'test.pdf',
      });
      
      expect(result['success'], true);
      expect(result['document_info'], isNotNull);
    });
  });
}
```

---

## 📈 监控和调试

### **性能监控**
- **内存使用监控**: 实时跟踪内存使用情况
- **CPU使用监控**: 监控计算密集型任务的CPU占用
- **FFI调用监控**: 跟踪FFI调用频率和耗时
- **数据库性能监控**: 监控查询性能和连接池状态

### **日志系统**
```rust
// 结构化日志
log::info!(
    "OCR处理完成: document_id={}, page={}, confidence={:.2}, duration={}ms",
    document_id, page_number, confidence, duration.as_millis()
);

// 性能日志
log::debug!(
    "FFI调用性能: function={}, duration={}ms, memory_used={}MB",
    function_name, duration.as_millis(), memory_mb
);
```

---

**🎯 这个架构设计确保了Flutter前端和Rust后端的高效协作，通过FFI实现了性能和安全的完美平衡，为智能PDF阅读器的核心功能提供了坚实的技术基础。**
