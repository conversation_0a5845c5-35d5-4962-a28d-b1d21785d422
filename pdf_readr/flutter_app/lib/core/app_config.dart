// 应用程序配置模块 (core/app_config.dart)
//
// 功能实现:
// ✅ 应用程序常量定义 (完整实现)
// ✅ 环境配置管理 (完整实现)
// ✅ 功能开关控制 (完整实现)
// ✅ 性能参数配置 (完整实现)
// ⚠️ 远程配置支持 (计划实现)
//
// 配置特点:
// - 编译时常量优化
// - 环境特定配置
// - 功能开关控制
// - 性能参数调优
//
// 法律合规:
// - 配置管理代码为原创实现
// - 不包含任何专有配置格式
// - 遵循Flutter最佳实践
//
// 作者: Augment Agent
// 创建时间: 2025-07-15
// 最后更新: 2025-07-15

library app_config;

import 'package:flutter/foundation.dart';
// import 'package:package_info_plus/package_info_plus.dart'; // 暂时移除
// import 'package:device_info_plus/device_info_plus.dart'; // 暂时移除

/// 应用程序配置类
///
/// 包含应用程序的所有配置常量和环境设置。
class AppConfig {
  // 私有构造函数，防止实例化
  AppConfig._();

  /// 应用程序基本信息
  static const String appName = 'PDF阅读器';
  static const String appDescription = '智能PDF阅读器 - 支持OCR、TTS和智能预加载';
  static const String appVersion = '1.0.0';
  static const int appBuildNumber = 1;

  /// 开发者信息
  static const String developerName = 'Augment Agent';
  static const String developerEmail = '<EMAIL>';
  static const String supportUrl =
      'https://github.com/augment/pdf-reader/issues';
  static const String privacyPolicyUrl =
      'https://github.com/augment/pdf-reader/privacy';

  /// 环境配置
  static bool get isDebugMode => kDebugMode;
  static bool get isReleaseMode => kReleaseMode;
  static bool get isProfileMode => kProfileMode;

  /// 平台检测
  static bool get isAndroid => defaultTargetPlatform == TargetPlatform.android;
  static bool get isIOS => defaultTargetPlatform == TargetPlatform.iOS;
  static bool get isWindows => defaultTargetPlatform == TargetPlatform.windows;
  static bool get isMacOS => defaultTargetPlatform == TargetPlatform.macOS;
  static bool get isLinux => defaultTargetPlatform == TargetPlatform.linux;
  static bool get isWeb => kIsWeb;
  static bool get isMobile => isAndroid || isIOS;
  static bool get isDesktop => isWindows || isMacOS || isLinux;

  /// 功能开关
  static const bool enableOCR = true;
  static const bool enableTTS = true;
  static const bool enablePreloading = true;
  static const bool enableAnalytics = false; // 隐私优先，默认关闭
  static const bool enableCrashReporting = false; // 隐私优先，默认关闭
  static const bool enablePerformanceMonitoring = kDebugMode;
  static const bool enableDetailedLogging = kDebugMode;

  /// 性能配置
  static const int maxConcurrentOCRTasks = 2;
  static const int maxPreloadPages = 5;
  static const int maxCacheSize = 256; // MB
  static const int maxFileSize = 500; // MB
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration cacheExpiration = Duration(hours: 24);

  /// UI配置
  static const double defaultFontSize = 16.0;
  static const double minFontSize = 12.0;
  static const double maxFontSize = 24.0;
  static const double defaultPagePadding = 16.0;
  static const double defaultBorderRadius = 8.0;
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);

  /// 文件配置
  static const List<String> supportedFileExtensions = [
    'pdf',
    'epub',
    'txt',
    'docx',
    'md',
  ];

  static const Map<String, String> mimeTypes = {
    'pdf': 'application/pdf',
    'epub': 'application/epub+zip',
    'txt': 'text/plain',
    'docx':
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'md': 'text/markdown',
  };

  /// OCR配置
  static const List<String> supportedOCRLanguages = [
    'eng', // 英语
    'chi_sim', // 简体中文
    'chi_tra', // 繁体中文
    'jpn', // 日语
    'kor', // 韩语
    'fra', // 法语
    'deu', // 德语
    'spa', // 西班牙语
    'rus', // 俄语
    'ara', // 阿拉伯语
  ];

  static const String defaultOCRLanguage = 'eng';
  static const double ocrConfidenceThreshold = 0.6;
  static const int ocrTimeoutSeconds = 30;

  /// TTS配置
  static const double defaultSpeakingRate = 1.0;
  static const double minSpeakingRate = 0.5;
  static const double maxSpeakingRate = 2.0;
  static const double defaultVolume = 0.8;
  static const String defaultVoice = 'default';

  /// 数据库配置
  static const String databaseName = 'pdf_reader.db';
  static const int databaseVersion = 1;
  static const String hiveDatabaseName = 'pdf_reader_hive';

  /// 缓存配置
  static const String cacheDirectoryName = 'pdf_reader_cache';
  static const String tempDirectoryName = 'pdf_reader_temp';
  static const String documentsDirectoryName = 'PDF Reader';

  /// 网络配置
  static const String apiBaseUrl = 'https://api.example.com'; // 如果需要网络功能
  static const String userAgent = '$appName/$appVersion';
  static const Map<String, String> defaultHeaders = {
    'User-Agent': userAgent,
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  };

  /// 日志配置
  static const String logFileName = 'pdf_reader.log';
  static const int maxLogFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxLogFiles = 5;

  /// 安全配置
  static const bool enableEncryption = true;
  static const String encryptionAlgorithm = 'AES-256-GCM';
  static const int keyDerivationIterations = 100000;

  /// 主题配置
  static const String defaultThemeMode = 'system'; // system, light, dark
  static const bool enableDynamicColor = true;
  static const bool enableMaterialYou = true;

  /// 辅助功能配置
  static const bool enableAccessibility = true;
  static const bool enableScreenReader = true;
  static const bool enableHighContrast = false;
  static const bool enableLargeText = false;

  /// 实验性功能
  static const bool enableExperimentalFeatures = kDebugMode;
  static const bool enableBetaFeatures = false;
  static const bool enableAlphaFeatures = false;
}

/// 运行时配置管理器
///
/// 管理需要在运行时获取的配置信息。
class RuntimeConfig {
  static RuntimeConfig? _instance;
  static RuntimeConfig get instance => _instance ??= RuntimeConfig._();

  RuntimeConfig._();

  // PackageInfo? _packageInfo; // 暂时移除
  // BaseDeviceInfo? _deviceInfo; // 暂时移除

  /// 包信息 - 简化版本
  String get appName => 'PDF阅读器';
  String get version => '1.0.0';
  String get buildNumber => '1';

  /// 设备信息 - 简化版本
  String get deviceModel => 'Unknown';
  String get osVersion => 'Unknown';

  /// 初始化运行时配置 - 简化版本
  Future<void> initialize() async {
    // 暂时不需要初始化，使用硬编码值
    if (kDebugMode) {
      debugPrint('运行时配置初始化完成');
    }
  }

  // 设备信息加载已简化，使用硬编码值

  // 复杂的getter方法已简化，使用上面定义的简单属性

  /// 检查是否为平板设备 - 简化版本
  bool get isTablet => false; // 暂时返回false
}

/// 功能标志管理器
///
/// 管理应用程序的功能开关。
class FeatureFlags {
  static const Map<String, bool> _flags = {
    'ocr_enabled': AppConfig.enableOCR,
    'tts_enabled': AppConfig.enableTTS,
    'preloading_enabled': AppConfig.enablePreloading,
    'analytics_enabled': AppConfig.enableAnalytics,
    'crash_reporting_enabled': AppConfig.enableCrashReporting,
    'performance_monitoring_enabled': AppConfig.enablePerformanceMonitoring,
    'detailed_logging_enabled': AppConfig.enableDetailedLogging,
    'experimental_features_enabled': AppConfig.enableExperimentalFeatures,
    'beta_features_enabled': AppConfig.enableBetaFeatures,
    'alpha_features_enabled': AppConfig.enableAlphaFeatures,
  };

  /// 检查功能是否启用
  static bool isEnabled(String feature) {
    return _flags[feature] ?? false;
  }

  /// 获取所有功能标志
  static Map<String, bool> getAllFlags() {
    return Map.from(_flags);
  }
}

/// 环境配置
enum Environment {
  development,
  staging,
  production,
}

/// 环境管理器
class EnvironmentManager {
  static Environment get current {
    if (AppConfig.isDebugMode) {
      return Environment.development;
    } else if (AppConfig.isProfileMode) {
      return Environment.staging;
    } else {
      return Environment.production;
    }
  }

  static bool get isDevelopment => current == Environment.development;
  static bool get isStaging => current == Environment.staging;
  static bool get isProduction => current == Environment.production;
}
