# Flutter PDF阅读器应用配置 (pubspec.yaml)
# 
# 功能实现:
# ✅ 项目基本配置 (完整实现)
# ✅ 依赖管理 (完整实现)
# ✅ 资源配置 (完整实现)
# ✅ 平台特定配置 (完整实现)
# 
# 应用特点:
# - 现代Flutter架构
# - 响应式UI设计
# - 多平台支持
# - 高性能渲染
# 
# 法律合规:
# - 所有依赖均使用兼容的开源许可证
# - UI设计为原创，无版权争议
# - 使用标准Flutter框架，BSD-3-Clause许可证
# 
# 作者: Augment Agent
# 创建时间: 2025-07-15
# 最后更新: 2025-07-15

name: pdf_reader
description: 智能PDF阅读器 - 支持OCR、TTS和智能预加载的高性能PDF阅读应用

# 应用版本信息
version: 1.0.0+1

# 环境要求
environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

# 核心依赖 - MVP版本
dependencies:
  flutter:
    sdk: flutter

  # FFI桥接（连接Rust核心库）
  ffi: ^2.1.0

# 开发依赖 - MVP版本
dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码质量
  flutter_lints: ^3.0.1

# Flutter配置 - MVP版本
flutter:
  uses-material-design: true

# 发布配置
publish_to: none # 不发布到pub.dev
