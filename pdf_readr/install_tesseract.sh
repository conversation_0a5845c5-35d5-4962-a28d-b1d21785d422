#!/bin/bash

# Tesseract OCR Linux/macOS 自动安装脚本

echo "========================================"
echo "    Tesseract OCR 自动安装脚本"
echo "========================================"
echo

# 检测操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    echo "🐧 检测到 Linux 系统"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    echo "🍎 检测到 macOS 系统"
else
    echo "❌ 不支持的操作系统: $OSTYPE"
    exit 1
fi

# Linux 安装函数
install_linux() {
    echo "📦 正在更新包列表..."
    sudo apt update

    echo "📦 正在安装 Tesseract OCR..."
    sudo apt install -y tesseract-ocr libtesseract-dev libleptonica-dev

    if [ $? -eq 0 ]; then
        echo "✅ Tesseract 核心安装成功"
    else
        echo "❌ Tesseract 核心安装失败"
        exit 1
    fi

    echo "🌍 正在安装语言包..."
    sudo apt install -y tesseract-ocr-eng tesseract-ocr-chi-sim tesseract-ocr-chi-tra

    if [ $? -eq 0 ]; then
        echo "✅ 语言包安装成功"
    else
        echo "⚠️  语言包安装失败，但核心功能可用"
    fi
}

# macOS 安装函数
install_macos() {
    # 检查 Homebrew 是否安装
    if ! command -v brew &> /dev/null; then
        echo "⚠️  Homebrew 未安装"
        echo "💡 正在安装 Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        
        if [ $? -ne 0 ]; then
            echo "❌ Homebrew 安装失败"
            echo "💡 请手动安装 Homebrew: https://brew.sh"
            exit 1
        fi
        echo "✅ Homebrew 安装成功"
    else
        echo "✅ Homebrew 已安装"
    fi

    echo "📦 正在安装 Tesseract OCR..."
    brew install tesseract

    if [ $? -eq 0 ]; then
        echo "✅ Tesseract 安装成功"
    else
        echo "❌ Tesseract 安装失败"
        exit 1
    fi

    echo "🌍 正在安装语言包..."
    brew install tesseract-lang

    if [ $? -eq 0 ]; then
        echo "✅ 语言包安装成功"
    else
        echo "⚠️  语言包安装失败，但核心功能可用"
    fi
}

# 验证安装函数
verify_installation() {
    echo
    echo "🔧 正在验证安装..."
    
    if command -v tesseract &> /dev/null; then
        echo "✅ Tesseract 命令可用"
        
        echo "📋 版本信息:"
        tesseract --version
        
        echo
        echo "🌍 可用语言:"
        tesseract --list-langs
        
        return 0
    else
        echo "❌ Tesseract 命令不可用"
        return 1
    fi
}

# 设置环境变量函数
setup_environment() {
    echo
    echo "🔧 设置环境变量..."
    
    # 检查常见的 tessdata 路径
    TESSDATA_PATHS=(
        "/usr/share/tesseract-ocr/5/tessdata"
        "/usr/share/tesseract-ocr/4.00/tessdata"
        "/usr/share/tessdata"
        "/opt/homebrew/share/tessdata"
        "/usr/local/share/tessdata"
    )
    
    for path in "${TESSDATA_PATHS[@]}"; do
        if [ -d "$path" ]; then
            echo "📁 找到 tessdata 目录: $path"
            
            # 添加到 shell 配置文件
            if [ -f "$HOME/.bashrc" ]; then
                echo "export TESSDATA_PREFIX=$path" >> "$HOME/.bashrc"
                echo "✅ 已添加到 ~/.bashrc"
            fi
            
            if [ -f "$HOME/.zshrc" ]; then
                echo "export TESSDATA_PREFIX=$path" >> "$HOME/.zshrc"
                echo "✅ 已添加到 ~/.zshrc"
            fi
            
            export TESSDATA_PREFIX="$path"
            break
        fi
    done
}

# 创建测试图像函数
create_test_image() {
    echo
    echo "🧪 创建测试图像..."
    
    if command -v convert &> /dev/null; then
        convert -size 400x100 xc:white -font Arial -pointsize 20 -fill black -gravity center -annotate +0+0 "Hello World Test" test_image.png
        echo "✅ 测试图像创建成功: test_image.png"
        
        echo "🧪 运行 OCR 测试..."
        tesseract test_image.png output -l eng
        
        if [ -f "output.txt" ]; then
            echo "📝 OCR 结果:"
            cat output.txt
            rm -f output.txt test_image.png
        fi
    else
        echo "⚠️  ImageMagick 未安装，跳过图像测试"
        echo "💡 可以安装 ImageMagick 进行图像测试:"
        if [ "$OS" = "linux" ]; then
            echo "   sudo apt install imagemagick"
        elif [ "$OS" = "macos" ]; then
            echo "   brew install imagemagick"
        fi
    fi
}

# 主安装流程
main() {
    # 根据操作系统选择安装方法
    if [ "$OS" = "linux" ]; then
        install_linux
    elif [ "$OS" = "macos" ]; then
        install_macos
    fi
    
    # 验证安装
    if verify_installation; then
        setup_environment
        create_test_image
        
        echo
        echo "========================================"
        echo "        安装完成！"
        echo "========================================"
        echo
        echo "✅ Tesseract OCR 安装成功"
        echo
        echo "📋 下一步："
        echo "1. 重启终端或运行: source ~/.bashrc (或 ~/.zshrc)"
        echo "2. 运行测试: cargo test --features tesseract"
        echo "3. 验证安装: tesseract --version"
        echo
        echo "🌍 检查可用语言: tesseract --list-langs"
        echo
    else
        echo
        echo "❌ 安装验证失败"
        echo "💡 请检查错误信息并手动安装"
        exit 1
    fi
}

# 运行主函数
main
