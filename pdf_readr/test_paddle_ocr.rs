// PaddleOCR 集成测试脚本
// 
// 使用方法：
// 1. 确保已安装 PaddleOCR 模型
// 2. 运行: cargo test --features paddle-ocr test_paddle_ocr_integration -- --nocapture

#[cfg(test)]
mod paddle_ocr_tests {
    use std::fs;
    use std::path::Path;
    
    use crate::ocr::paddle_ocr_engine::{PaddleOCREngine, PaddleOCRConfig};
    use crate::ocr::engine::{ImageData, ImageFormat, OCREngine};
    
    /// 测试 PaddleOCR 基本功能
    #[test]
    fn test_paddle_ocr_basic() {
        println!("🧪 测试 PaddleOCR 基本功能...");
        
        let config = PaddleOCRConfig::default();
        let engine_result = PaddleOCREngine::new(config);
        
        match engine_result {
            Ok(engine) => {
                println!("✅ PaddleOCR 引擎创建成功");
                
                let info = engine.get_engine_info();
                println!("📊 引擎信息: {:?}", info);
                
                let stats = engine.get_statistics();
                println!("📈 统计信息: {:?}", stats);
            }
            Err(e) => {
                #[cfg(feature = "paddle-ocr")]
                {
                    println!("❌ PaddleOCR 引擎创建失败: {}", e);
                    println!("💡 请确保已安装 PaddleOCR 模型文件");
                }
                
                #[cfg(not(feature = "paddle-ocr"))]
                {
                    println!("⚠️  PaddleOCR 特性未启用，这是预期的");
                    println!("💡 使用 cargo test --features paddle-ocr 来启用");
                }
            }
        }
    }
    
    /// 测试 PaddleOCR 文本识别
    #[test]
    fn test_paddle_ocr_recognition() {
        println!("🧪 测试 PaddleOCR 文本识别...");
        
        let config = PaddleOCRConfig::default();
        let mut engine = match PaddleOCREngine::new(config) {
            Ok(engine) => engine,
            Err(e) => {
                println!("⚠️  无法创建 PaddleOCR 引擎: {}", e);
                return;
            }
        };
        
        // 创建测试图像数据
        let test_image = create_test_chinese_image();
        
        // 执行识别
        match engine.recognize_image(&test_image) {
            Ok(result) => {
                println!("✅ PaddleOCR 识别成功");
                println!("📝 识别文本: '{}'", result.text);
                println!("🎯 置信度: {:.3}", result.confidence);
                println!("⏱️  处理时间: {:?}", result.processing_time);
                println!("🔍 检测区域数: {}", result.detection_count);
                
                // 验证结果
                assert!(!result.text.is_empty(), "识别的文本不应为空");
                assert!(result.confidence >= 0.0 && result.confidence <= 1.0, "置信度应在0-1范围内");
                assert!(!result.text_regions.is_empty(), "应该有文本区域");
            }
            Err(e) => {
                println!("⚠️  PaddleOCR 识别失败: {}", e);
                
                #[cfg(not(feature = "paddle-ocr"))]
                {
                    println!("💡 这可能是因为 paddle-ocr 特性未启用");
                }
            }
        }
    }
    
    /// 测试 PaddleOCR 批量处理
    #[test]
    fn test_paddle_ocr_batch() {
        println!("🧪 测试 PaddleOCR 批量处理...");
        
        let config = PaddleOCRConfig::default();
        let mut engine = match PaddleOCREngine::new(config) {
            Ok(engine) => engine,
            Err(e) => {
                println!("⚠️  无法创建 PaddleOCR 引擎: {}", e);
                return;
            }
        };
        
        // 创建多个测试图像
        let test_images = vec![
            create_test_chinese_image(),
            create_test_english_image(),
            create_test_mixed_image(),
        ];
        
        // 执行批量识别
        match engine.recognize_batch(&test_images) {
            Ok(results) => {
                println!("✅ PaddleOCR 批量识别完成");
                println!("📊 处理图像数: {}", results.len());
                
                for (i, result) in results.iter().enumerate() {
                    println!("📝 图像 {}: '{}' (置信度: {:.3})", 
                            i + 1, result.text, result.confidence);
                }
                
                assert_eq!(results.len(), test_images.len(), "结果数量应与输入图像数量一致");
            }
            Err(e) => {
                println!("⚠️  PaddleOCR 批量识别失败: {}", e);
            }
        }
    }
    
    /// 测试 OCR 引擎 trait 实现
    #[test]
    fn test_paddle_ocr_trait() {
        println!("🧪 测试 PaddleOCR OCR 引擎 trait...");
        
        let config = PaddleOCRConfig::default();
        let mut engine = match PaddleOCREngine::new(config) {
            Ok(engine) => engine,
            Err(e) => {
                println!("⚠️  无法创建 PaddleOCR 引擎: {}", e);
                return;
            }
        };
        
        // 测试引擎信息
        let engine_name = engine.get_engine_name();
        println!("🏷️  引擎名称: {}", engine_name);
        assert_eq!(engine_name, "PaddleOCR");
        
        // 测试支持的语言
        let languages = engine.get_supported_languages();
        println!("🌍 支持的语言: {:?}", languages);
        assert!(!languages.is_empty(), "应该支持至少一种语言");
        assert!(languages.contains(&"chi_sim".to_string()), "应该支持中文简体");
        
        // 测试语言设置
        let result = engine.set_language("chi_sim");
        assert!(result.is_ok(), "语言设置应该成功");
        
        // 测试文本识别
        let test_image = create_test_chinese_image();
        match engine.recognize_text(&test_image) {
            Ok(result) => {
                println!("✅ OCR trait 识别成功");
                println!("📝 识别文本: '{}'", result.recognized_text);
                println!("🎯 置信度: {:.3}", result.confidence_score);
                println!("🌍 语言: {}", result.language);
                
                assert!(!result.recognized_text.is_empty(), "识别的文本不应为空");
                assert!(result.confidence_score >= 0.0 && result.confidence_score <= 1.0, "置信度应在0-1范围内");
            }
            Err(e) => {
                println!("⚠️  OCR trait 识别失败: {}", e);
            }
        }
    }
    
    /// 测试模型文件检查
    #[test]
    fn test_model_files() {
        println!("🧪 测试 PaddleOCR 模型文件...");
        
        let config = PaddleOCRConfig::default();
        
        // 检查模型目录
        if Path::new(&config.model_dir).exists() {
            println!("✅ 模型目录存在: {}", config.model_dir);
        } else {
            println!("⚠️  模型目录不存在: {}", config.model_dir);
            println!("💡 请下载 PaddleOCR 模型文件到指定目录");
        }
        
        // 检查各个模型文件
        let model_files = vec![
            ("检测模型", &config.det_model_path),
            ("识别模型", &config.rec_model_path),
            ("分类模型", &config.cls_model_path),
            ("字符字典", &config.char_dict_path),
        ];
        
        for (name, path) in model_files {
            if Path::new(path).exists() {
                println!("✅ {} 存在: {}", name, path);
            } else {
                println!("⚠️  {} 不存在: {}", name, path);
            }
        }
    }
    
    /// 创建中文测试图像
    fn create_test_chinese_image() -> ImageData {
        // 创建一个包含中文文本的测试图像
        let width = 200;
        let height = 60;
        let mut image_data = Vec::new();
        
        // 创建白底图像（RGB格式）
        for y in 0..height {
            for x in 0..width {
                // 在中间区域模拟中文字符
                if y >= 15 && y <= 45 && x >= 20 && x <= 180 {
                    // 模拟中文字符的复杂笔画
                    let char_pattern = (x / 40) % 4; // 4个字符区域
                    let is_stroke = match char_pattern {
                        0 => (x % 8 < 4 && y % 6 < 3) || (x % 12 < 2), // 复杂笔画1
                        1 => (x % 6 < 3 && y % 8 < 4) || (y % 10 < 2), // 复杂笔画2
                        2 => (x % 10 < 5 && y % 4 < 2) || (x % 8 < 1), // 复杂笔画3
                        _ => (x % 7 < 3 && y % 7 < 3) || (y % 9 < 1),  // 复杂笔画4
                    };
                    
                    if is_stroke {
                        image_data.push(0);   // R - 黑色
                        image_data.push(0);   // G - 黑色
                        image_data.push(0);   // B - 黑色
                    } else {
                        image_data.push(255); // R - 白色
                        image_data.push(255); // G - 白色
                        image_data.push(255); // B - 白色
                    }
                } else {
                    image_data.push(255); // R - 白色背景
                    image_data.push(255); // G - 白色背景
                    image_data.push(255); // B - 白色背景
                }
            }
        }
        
        ImageData {
            data: image_data,
            width,
            height,
            format: ImageFormat::Png,
        }
    }
    
    /// 创建英文测试图像
    fn create_test_english_image() -> ImageData {
        // 创建一个包含英文文本的测试图像
        let width = 150;
        let height = 40;
        let mut image_data = Vec::new();
        
        // 创建白底图像（RGB格式）
        for y in 0..height {
            for x in 0..width {
                // 在中间区域模拟英文字符 "Hello"
                if y >= 10 && y <= 30 && x >= 10 && x <= 140 {
                    let char_pattern = (x - 10) / 25; // 5个字符区域
                    let is_stroke = match char_pattern {
                        0 => x % 4 < 2 || (y >= 18 && y <= 22), // H
                        1 => x % 4 < 2,                         // I (简化)
                        2 => (y >= 28 && y <= 30) || (x % 6 < 2 && y >= 10 && y <= 15), // L
                        3 => (y >= 28 && y <= 30) || (x % 6 < 2 && y >= 10 && y <= 15), // L
                        _ => (y >= 15 && y <= 25) && (x % 8 < 6), // O
                    };
                    
                    if is_stroke {
                        image_data.push(0);   // R - 黑色
                        image_data.push(0);   // G - 黑色
                        image_data.push(0);   // B - 黑色
                    } else {
                        image_data.push(255); // R - 白色
                        image_data.push(255); // G - 白色
                        image_data.push(255); // B - 白色
                    }
                } else {
                    image_data.push(255); // R - 白色背景
                    image_data.push(255); // G - 白色背景
                    image_data.push(255); // B - 白色背景
                }
            }
        }
        
        ImageData {
            data: image_data,
            width,
            height,
            format: ImageFormat::Png,
        }
    }
    
    /// 创建中英文混合测试图像
    fn create_test_mixed_image() -> ImageData {
        // 创建一个包含中英文混合文本的测试图像
        let width = 300;
        let height = 80;
        let mut image_data = Vec::new();
        
        // 创建白底图像（RGB格式）
        for y in 0..height {
            for x in 0..width {
                // 上半部分：英文，下半部分：中文
                if (y >= 10 && y <= 35 && x >= 20 && x <= 280) || 
                   (y >= 45 && y <= 70 && x >= 20 && x <= 280) {
                    let is_english_area = y <= 35;
                    let is_stroke = if is_english_area {
                        // 英文区域的简单模式
                        (x % 15 < 8) && (y % 8 < 4)
                    } else {
                        // 中文区域的复杂模式
                        ((x % 20 < 10) && (y % 6 < 3)) || (x % 25 < 3)
                    };
                    
                    if is_stroke {
                        image_data.push(0);   // R - 黑色
                        image_data.push(0);   // G - 黑色
                        image_data.push(0);   // B - 黑色
                    } else {
                        image_data.push(255); // R - 白色
                        image_data.push(255); // G - 白色
                        image_data.push(255); // B - 白色
                    }
                } else {
                    image_data.push(255); // R - 白色背景
                    image_data.push(255); // G - 白色背景
                    image_data.push(255); // B - 白色背景
                }
            }
        }
        
        ImageData {
            data: image_data,
            width,
            height,
            format: ImageFormat::Png,
        }
    }
}

/// 主函数 - 用于独立运行测试
#[cfg(feature = "paddle-ocr")]
fn main() {
    println!("🚀 开始 PaddleOCR 集成测试...");
    
    // 运行基本测试
    paddle_ocr_tests::test_paddle_ocr_basic();
    paddle_ocr_tests::test_paddle_ocr_recognition();
    paddle_ocr_tests::test_paddle_ocr_batch();
    paddle_ocr_tests::test_paddle_ocr_trait();
    paddle_ocr_tests::test_model_files();
    
    println!("🎉 所有测试完成！");
}

#[cfg(not(feature = "paddle-ocr"))]
fn main() {
    println!("⚠️  paddle-ocr 特性未启用");
    println!("💡 使用以下命令启用并运行测试:");
    println!("   cargo run --features paddle-ocr --bin test_paddle_ocr");
}
