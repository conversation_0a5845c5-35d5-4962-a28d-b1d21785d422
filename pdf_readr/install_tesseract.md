# Tesseract OCR 安装指南

## 🚨 重要说明
我无法直接在您的系统上安装软件，但我可以为您提供详细的安装指导和配置文件。

## 📋 安装步骤

### Windows 安装

#### 方法1：使用 Chocolatey（推荐）
```bash
# 如果您已安装 Chocolatey
choco install tesseract

# 安装语言包
choco install tesseract-languages
```

#### 方法2：手动安装
1. 访问 https://github.com/UB-Mannheim/tesseract/wiki
2. 下载最新版本：`tesseract-ocr-w64-setup-v5.x.x.exe`
3. 运行安装程序
4. 在安装过程中选择语言包：
   - ✅ English (eng)
   - ✅ Chinese Simplified (chi_sim)
   - ✅ Chinese Traditional (chi_tra)
5. 记住安装路径（通常是 `C:\Program Files\Tesseract-OCR`）
6. 将安装路径添加到系统 PATH 环境变量

#### 设置环境变量（Windows）
```bash
# 添加到系统 PATH
C:\Program Files\Tesseract-OCR

# 设置 Tesseract 数据路径（可选）
TESSDATA_PREFIX=C:\Program Files\Tesseract-OCR\tessdata
```

### macOS 安装

```bash
# 使用 Homebrew
brew install tesseract

# 安装语言包
brew install tesseract-lang

# 验证安装
tesseract --version
tesseract --list-langs
```

### Linux (Ubuntu/Debian) 安装

```bash
# 更新包列表
sudo apt update

# 安装 Tesseract 和开发库
sudo apt install tesseract-ocr libtesseract-dev libleptonica-dev

# 安装语言包
sudo apt install tesseract-ocr-eng tesseract-ocr-chi-sim tesseract-ocr-chi-tra

# 验证安装
tesseract --version
tesseract --list-langs
```

### CentOS/RHEL 安装

```bash
# 启用 EPEL 仓库
sudo yum install epel-release

# 安装 Tesseract
sudo yum install tesseract tesseract-devel tesseract-langpack-eng tesseract-langpack-chi_sim

# 验证安装
tesseract --version
```

## 🔧 验证安装

### 命令行测试
```bash
# 检查版本
tesseract --version

# 检查可用语言
tesseract --list-langs

# 测试 OCR（需要准备一个测试图片）
tesseract test_image.png output -l eng
```

### 预期输出
```
tesseract 5.x.x
 leptonica-1.x.x
  libgif 5.x.x : libjpeg 8d (libjpeg-turbo 2.x.x) : libpng 1.x.x : libtiff 4.x.x : zlib 1.x.x : libwebp 1.x.x : libopenjp2 2.x.x

Available languages:
chi_sim
chi_tra
eng
osd
```

## 📦 Rust 项目配置

### 1. 启用 Tesseract 特性
```bash
# 编译时启用 tesseract 特性
cargo build --features tesseract

# 运行测试
cargo test --features tesseract
```

### 2. 环境变量设置（如果需要）
```bash
# Linux/macOS
export TESSERACT_INCLUDE_DIR=/usr/include/tesseract
export TESSERACT_LIB_DIR=/usr/lib

# Windows (PowerShell)
$env:TESSERACT_INCLUDE_DIR="C:\Program Files\Tesseract-OCR\include"
$env:TESSERACT_LIB_DIR="C:\Program Files\Tesseract-OCR\lib"
```

## 🧪 测试 Tesseract 集成

### 创建测试图像
您可以创建一个简单的文本图像进行测试，或者使用以下命令创建测试图像：

```bash
# 使用 ImageMagick 创建测试图像（如果已安装）
convert -size 400x100 xc:white -font Arial -pointsize 20 -fill black -gravity center -annotate +0+0 "Hello World Test" test_image.png
```

### 运行测试
```bash
# 运行 OCR 测试
cargo test test_tesseract_integration --features tesseract -- --nocapture
```

## ❗ 常见问题

### 问题1：找不到 Tesseract 库
**解决方案：**
- 确保 Tesseract 已正确安装
- 检查 PATH 环境变量
- 设置 TESSERACT_INCLUDE_DIR 和 TESSERACT_LIB_DIR

### 问题2：语言包缺失
**解决方案：**
- 安装所需的语言包
- 检查 tessdata 目录是否包含语言文件
- 设置 TESSDATA_PREFIX 环境变量

### 问题3：编译错误
**解决方案：**
- 确保安装了开发库（libtesseract-dev）
- 检查 pkg-config 是否可用
- 尝试重新安装 Tesseract

## 📞 获取帮助

如果您在安装过程中遇到问题，请：

1. 检查 Tesseract 官方文档
2. 运行 `tesseract --version` 确认安装
3. 检查系统日志中的错误信息
4. 提供具体的错误信息以便进一步协助

## 🎯 下一步

安装完成后，我们可以：
1. 测试 Tesseract 集成
2. 优化 OCR 参数
3. 添加更多语言支持
4. 实现批量 OCR 处理
