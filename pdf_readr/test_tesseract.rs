// Tesseract OCR 集成测试脚本
// 
// 使用方法：
// 1. 确保已安装 Tesseract OCR
// 2. 运行: cargo test --features tesseract test_tesseract_integration -- --nocapture

#[cfg(test)]
mod tesseract_tests {
    use std::fs;
    use std::path::Path;
    
    #[cfg(feature = "tesseract")]
    use tesseract::Tesseract;
    
    /// 测试 Tesseract 基本功能
    #[test]
    #[cfg(feature = "tesseract")]
    fn test_tesseract_basic() {
        println!("🧪 测试 Tesseract 基本功能...");
        
        // 创建 Tesseract 实例
        let tesseract = Tesseract::new(None, Some("eng"));
        
        match tesseract {
            Ok(mut tess) => {
                println!("✅ Tesseract 初始化成功");
                
                // 获取版本信息
                if let Ok(version) = tess.get_version() {
                    println!("📋 Tesseract 版本: {}", version);
                }
                
                // 获取可用语言
                if let Ok(languages) = tess.get_available_languages() {
                    println!("🌍 可用语言: {:?}", languages);
                } else {
                    println!("⚠️  无法获取语言列表");
                }
            }
            Err(e) => {
                panic!("❌ Tesseract 初始化失败: {}", e);
            }
        }
    }
    
    /// 测试文本识别功能
    #[test]
    #[cfg(feature = "tesseract")]
    fn test_tesseract_text_recognition() {
        println!("🧪 测试 Tesseract 文本识别...");
        
        // 创建简单的测试图像数据（白底黑字）
        let test_image_data = create_test_image();
        
        let mut tesseract = Tesseract::new(None, Some("eng"))
            .expect("无法初始化 Tesseract");
        
        // 设置图像数据
        tesseract.set_image_from_mem(&test_image_data)
            .expect("无法设置图像数据");
        
        // 执行 OCR
        match tesseract.get_text() {
            Ok(text) => {
                println!("✅ OCR 识别成功");
                println!("📝 识别文本: '{}'", text.trim());
                
                // 获取置信度
                let confidence = tesseract.mean_text_conf();
                println!("🎯 置信度: {}%", confidence);
                
                assert!(!text.trim().is_empty(), "识别的文本不应为空");
            }
            Err(e) => {
                panic!("❌ OCR 识别失败: {}", e);
            }
        }
    }
    
    /// 测试中文识别功能
    #[test]
    #[cfg(feature = "tesseract")]
    fn test_tesseract_chinese_recognition() {
        println!("🧪 测试 Tesseract 中文识别...");
        
        let tesseract = Tesseract::new(None, Some("chi_sim"));
        
        match tesseract {
            Ok(mut tess) => {
                println!("✅ 中文 Tesseract 初始化成功");
                
                // 这里可以添加中文图像测试
                // 由于没有实际的中文图像，我们只测试初始化
                
                if let Ok(languages) = tess.get_available_languages() {
                    if languages.contains(&"chi_sim".to_string()) {
                        println!("✅ 中文简体语言包可用");
                    } else {
                        println!("⚠️  中文简体语言包不可用");
                    }
                }
            }
            Err(e) => {
                println!("⚠️  中文 Tesseract 初始化失败: {}", e);
                println!("💡 请确保已安装中文语言包: tesseract-ocr-chi-sim");
            }
        }
    }
    
    /// 测试我们的 Tesseract 集成模块
    #[test]
    fn test_our_tesseract_integration() {
        println!("🧪 测试我们的 Tesseract 集成模块...");
        
        // 测试配置创建
        let config = crate::ocr::minimal_modules::real_tesseract_engine::TesseractConfig {
            default_language: "eng".to_string(),
            confidence_threshold: 60.0,
            max_retry_attempts: 3,
            timeout_seconds: 30,
            page_seg_mode: 6,
            enable_whitelist: false,
            whitelist_chars: String::new(),
        };
        
        println!("✅ Tesseract 配置创建成功");
        println!("📋 默认语言: {}", config.default_language);
        println!("🎯 置信度阈值: {}%", config.confidence_threshold);
        
        // 测试引擎创建
        let engine_result = crate::ocr::minimal_modules::real_tesseract_engine::RealTesseractEngine::new(config);
        
        match engine_result {
            Ok(engine) => {
                println!("✅ Tesseract 引擎创建成功");
                
                // 测试语言支持检查
                assert!(engine.is_language_supported("eng"), "应该支持英语");
                
                // 获取引擎信息
                let info = engine.get_engine_info();
                println!("📊 引擎信息: {:?}", info);
            }
            Err(e) => {
                println!("⚠️  Tesseract 引擎创建失败: {}", e);
                
                #[cfg(not(feature = "tesseract"))]
                {
                    println!("💡 这是预期的，因为 tesseract 特性未启用");
                    println!("💡 使用 cargo test --features tesseract 来启用");
                }
            }
        }
    }
    
    /// 创建简单的测试图像数据
    fn create_test_image() -> Vec<u8> {
        // 创建一个简单的 24x8 像素的测试图像
        // 这是一个非常简化的示例，实际应用中需要正确的图像格式
        
        let width = 100;
        let height = 30;
        let mut image_data = Vec::new();
        
        // 创建白底图像（RGB格式）
        for y in 0..height {
            for x in 0..width {
                // 在中间区域绘制一些"文字"（黑色像素）
                if y >= 10 && y <= 20 && x >= 10 && x <= 90 {
                    // 简单的文字模式
                    if (x >= 15 && x <= 25) || (x >= 35 && x <= 45) || (x >= 55 && x <= 65) {
                        image_data.push(0);   // R - 黑色
                        image_data.push(0);   // G - 黑色
                        image_data.push(0);   // B - 黑色
                    } else {
                        image_data.push(255); // R - 白色
                        image_data.push(255); // G - 白色
                        image_data.push(255); // B - 白色
                    }
                } else {
                    image_data.push(255); // R - 白色背景
                    image_data.push(255); // G - 白色背景
                    image_data.push(255); // B - 白色背景
                }
            }
        }
        
        image_data
    }
    
    /// 测试系统环境
    #[test]
    fn test_system_environment() {
        println!("🧪 测试系统环境...");
        
        // 检查环境变量
        if let Ok(tessdata) = std::env::var("TESSDATA_PREFIX") {
            println!("📁 TESSDATA_PREFIX: {}", tessdata);
        } else {
            println!("⚠️  TESSDATA_PREFIX 环境变量未设置");
        }
        
        // 检查常见的 Tesseract 安装路径
        let common_paths = vec![
            "/usr/bin/tesseract",
            "/usr/local/bin/tesseract",
            "C:\\Program Files\\Tesseract-OCR\\tesseract.exe",
            "/opt/homebrew/bin/tesseract",
        ];
        
        for path in common_paths {
            if Path::new(path).exists() {
                println!("✅ 找到 Tesseract: {}", path);
                return;
            }
        }
        
        println!("⚠️  未在常见路径找到 Tesseract");
        println!("💡 请确保 Tesseract 已安装并在 PATH 中");
    }
}

/// 主函数 - 用于独立运行测试
#[cfg(feature = "tesseract")]
fn main() {
    println!("🚀 开始 Tesseract 集成测试...");
    
    // 运行基本测试
    tesseract_tests::test_tesseract_basic();
    tesseract_tests::test_tesseract_text_recognition();
    tesseract_tests::test_tesseract_chinese_recognition();
    tesseract_tests::test_our_tesseract_integration();
    tesseract_tests::test_system_environment();
    
    println!("🎉 所有测试完成！");
}

#[cfg(not(feature = "tesseract"))]
fn main() {
    println!("⚠️  tesseract 特性未启用");
    println!("💡 使用以下命令启用并运行测试:");
    println!("   cargo run --features tesseract --bin test_tesseract");
}
