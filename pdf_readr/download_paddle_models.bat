@echo off
REM PaddleOCR 模型自动下载脚本 (Windows 版本)

echo ========================================
echo     PaddleOCR 模型自动下载脚本
echo ========================================
echo.

REM 设置模型目录
set MODEL_DIR=.\models\paddle_ocr
set TEMP_DIR=.\temp_downloads

REM 创建目录
echo 📁 创建模型目录...
if not exist "%MODEL_DIR%\det" mkdir "%MODEL_DIR%\det"
if not exist "%MODEL_DIR%\rec" mkdir "%MODEL_DIR%\rec"
if not exist "%MODEL_DIR%\cls" mkdir "%MODEL_DIR%\cls"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

REM 模型下载 URL
set DET_MODEL_URL=https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_det_infer.tar
set REC_MODEL_URL=https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_rec_infer.tar
set CLS_MODEL_URL=https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar
set DICT_URL=https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.6/ppocr/utils/ppocr_keys_v1.txt

REM 检查下载工具
echo 🔍 检查下载工具...
where curl >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 使用 curl 下载
    set DOWNLOAD_CMD=curl -L -o
) else (
    echo ❌ 未找到 curl，请安装 curl 或手动下载模型
    echo 💡 可以通过以下方式安装 curl:
    echo    - Windows 10/11: 系统自带 curl
    echo    - 旧版本 Windows: 下载 Git for Windows 或安装 curl
    pause
    exit /b 1
)

REM 检查解压工具
where tar >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 使用 tar 解压
    set EXTRACT_CMD=tar -xf
) else (
    echo ❌ 未找到 tar，请安装 tar 或手动解压
    echo 💡 Windows 10/11 系统自带 tar 命令
    pause
    exit /b 1
)

echo.
echo 🚀 开始下载 PaddleOCR 模型...

REM 下载检测模型
echo.
echo 📦 下载检测模型...
echo 🔗 URL: %DET_MODEL_URL%
%DOWNLOAD_CMD% "%TEMP_DIR%\det_model.tar" "%DET_MODEL_URL%"
if %errorLevel% == 0 (
    echo ✅ 检测模型下载成功
    echo 📂 解压检测模型...
    %EXTRACT_CMD% "%TEMP_DIR%\det_model.tar" -C "%MODEL_DIR%\det" --strip-components=1
    if %errorLevel% == 0 (
        echo ✅ 检测模型解压成功
        del "%TEMP_DIR%\det_model.tar"
    ) else (
        echo ❌ 检测模型解压失败
        goto error
    )
) else (
    echo ❌ 检测模型下载失败
    goto error
)

REM 下载识别模型
echo.
echo 📦 下载识别模型...
echo 🔗 URL: %REC_MODEL_URL%
%DOWNLOAD_CMD% "%TEMP_DIR%\rec_model.tar" "%REC_MODEL_URL%"
if %errorLevel% == 0 (
    echo ✅ 识别模型下载成功
    echo 📂 解压识别模型...
    %EXTRACT_CMD% "%TEMP_DIR%\rec_model.tar" -C "%MODEL_DIR%\rec" --strip-components=1
    if %errorLevel% == 0 (
        echo ✅ 识别模型解压成功
        del "%TEMP_DIR%\rec_model.tar"
    ) else (
        echo ❌ 识别模型解压失败
        goto error
    )
) else (
    echo ❌ 识别模型下载失败
    goto error
)

REM 下载分类模型
echo.
echo 📦 下载分类模型...
echo 🔗 URL: %CLS_MODEL_URL%
%DOWNLOAD_CMD% "%TEMP_DIR%\cls_model.tar" "%CLS_MODEL_URL%"
if %errorLevel% == 0 (
    echo ✅ 分类模型下载成功
    echo 📂 解压分类模型...
    %EXTRACT_CMD% "%TEMP_DIR%\cls_model.tar" -C "%MODEL_DIR%\cls" --strip-components=1
    if %errorLevel% == 0 (
        echo ✅ 分类模型解压成功
        del "%TEMP_DIR%\cls_model.tar"
    ) else (
        echo ❌ 分类模型解压失败
        goto error
    )
) else (
    echo ❌ 分类模型下载失败
    goto error
)

REM 下载字符字典
echo.
echo 📚 下载字符字典...
echo 🔗 URL: %DICT_URL%
%DOWNLOAD_CMD% "%MODEL_DIR%\ppocr_keys_v1.txt" "%DICT_URL%"
if %errorLevel% == 0 (
    echo ✅ 字符字典下载成功
) else (
    echo ❌ 字符字典下载失败
    goto error
)

REM 验证模型文件
echo.
echo 🔍 验证模型文件...

set ALL_GOOD=1

REM 检查检测模型
if exist "%MODEL_DIR%\det\inference.pdmodel" if exist "%MODEL_DIR%\det\inference.pdiparams" (
    echo ✅ 检测模型文件完整
) else (
    echo ❌ 检测模型文件缺失
    set ALL_GOOD=0
)

REM 检查识别模型
if exist "%MODEL_DIR%\rec\inference.pdmodel" if exist "%MODEL_DIR%\rec\inference.pdiparams" (
    echo ✅ 识别模型文件完整
) else (
    echo ❌ 识别模型文件缺失
    set ALL_GOOD=0
)

REM 检查分类模型
if exist "%MODEL_DIR%\cls\inference.pdmodel" if exist "%MODEL_DIR%\cls\inference.pdiparams" (
    echo ✅ 分类模型文件完整
) else (
    echo ❌ 分类模型文件缺失
    set ALL_GOOD=0
)

REM 检查字典文件
if exist "%MODEL_DIR%\ppocr_keys_v1.txt" (
    echo ✅ 字符字典文件存在
) else (
    echo ❌ 字符字典文件缺失
    set ALL_GOOD=0
)

if %ALL_GOOD% == 1 (
    echo.
    echo ========================================
    echo         下载完成！
    echo ========================================
    echo.
    echo ✅ 所有 PaddleOCR 模型下载成功
    echo.
    echo 📋 下一步：
    echo 1. 编译项目: cargo build --features paddle-ocr
    echo 2. 运行测试: cargo test --features paddle-ocr
    echo 3. 开始使用 PaddleOCR 进行文本识别
    echo.
    
    REM 显示模型信息
    echo 📊 模型文件信息:
    echo ----------------------------------------
    echo 📁 模型目录: %MODEL_DIR%
    echo.
    echo 📂 检测模型:
    if exist "%MODEL_DIR%\det\inference.pdmodel" dir "%MODEL_DIR%\det\inference.*" /B
    echo.
    echo 📂 识别模型:
    if exist "%MODEL_DIR%\rec\inference.pdmodel" dir "%MODEL_DIR%\rec\inference.*" /B
    echo.
    echo 📂 分类模型:
    if exist "%MODEL_DIR%\cls\inference.pdmodel" dir "%MODEL_DIR%\cls\inference.*" /B
    echo.
    echo 📚 字符字典:
    if exist "%MODEL_DIR%\ppocr_keys_v1.txt" dir "%MODEL_DIR%\ppocr_keys_v1.txt" /B
    
    goto success
) else (
    echo.
    echo ❌ 部分模型文件缺失或损坏
    goto error
)

:success
REM 清理临时文件
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
echo.
echo 🎉 PaddleOCR 模型安装完成！
goto end

:error
echo.
echo ❌ 模型下载或验证失败
echo 💡 请检查网络连接并重试
echo 💡 或者手动下载模型文件到指定目录
echo.
echo 📋 手动下载说明：
echo 1. 访问: https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.6/doc/doc_ch/models_list.md
echo 2. 下载所需的模型文件
echo 3. 解压到 %MODEL_DIR% 目录
echo.
REM 清理临时文件
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
exit /b 1

:end
echo 按任意键退出...
pause >nul
