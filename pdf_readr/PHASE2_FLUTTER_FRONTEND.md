# 📋 第二阶段: Flutter前端开发计划

## 🎯 阶段目标
实现完整的Flutter前端界面系统，提供优秀的用户体验。

## ⏱️ 时间安排: 3-4周

---

## 📖 **Week 1-1.5: 主阅读界面开发**

### **优先级1: PDF阅读器组件 (4天)**

#### **模块**: `flutter_app/lib/widgets/pdf_reader_widget.dart`
```dart
/// 🧩 PDF阅读器组件 - 最小模块化设计
/// 
/// 功能实现:
/// ✅ 高质量PDF页面显示 (计划实现)
/// ✅ 流畅的翻页动画 (计划实现)
/// ✅ 缩放和平移手势 (计划实现)
/// ✅ 页面预加载机制 (计划实现)
///
/// 设计原则:
/// - 单一职责: 仅负责PDF页面的显示和基础交互
/// - 最小模块化: 代码少于200行，接口简洁
/// - 高性能: 60fps流畅渲染
/// - 响应式设计: 适配不同屏幕尺寸
```

**核心功能**:
1. **页面渲染**: 高质量PDF页面显示
2. **手势支持**: 双指缩放、单指平移、点击翻页
3. **翻页动画**: 平滑的翻页过渡效果
4. **预加载**: 智能的页面预加载机制

**实现重点**:
```dart
class PDFReaderWidget extends StatefulWidget {
  final String documentPath;
  final int initialPage;
  final Function(int)? onPageChanged;
  
  @override
  _PDFReaderWidgetState createState() => _PDFReaderWidgetState();
}

class _PDFReaderWidgetState extends State<PDFReaderWidget> {
  late PageController _pageController;
  late TransformationController _transformationController;
  
  @override
  Widget build(BuildContext context) {
    return InteractiveViewer(
      transformationController: _transformationController,
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: _handlePageChanged,
        itemBuilder: (context, index) => _buildPage(index),
      ),
    );
  }
  
  Widget _buildPage(int pageIndex) {
    return FutureBuilder<PDFPage>(
      future: _loadPage(pageIndex),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return PDFPageWidget(page: snapshot.data!);
        }
        return const CircularProgressIndicator();
      },
    );
  }
}
```

#### **模块**: `flutter_app/lib/widgets/reading_toolbar.dart`
```dart
/// 🧩 阅读工具栏 - 最小模块化设计
/// 
/// 功能实现:
/// ✅ 页面导航控制 (计划实现)
/// ✅ 缩放比例控制 (计划实现)
/// ✅ 阅读模式切换 (计划实现)
/// ✅ 工具栏自动隐藏 (计划实现)
```

### **优先级2: 阅读控制工具栏 (3天)**

**核心功能**:
1. **页面导航**: 上一页/下一页/跳转到指定页
2. **缩放控制**: 缩放比例调节和适应屏幕
3. **模式切换**: 原文模式/重排模式/对照模式
4. **自动隐藏**: 阅读时工具栏自动隐藏

---

## 📱 **Week 1.5-3: 对照编辑界面开发**

### **优先级1: 分屏布局组件 (5天)**

#### **模块**: `flutter_app/lib/widgets/split_view_widget.dart`
```dart
/// 🧩 分屏视图组件 - 最小模块化设计
/// 
/// 功能实现:
/// ✅ 左右分屏显示 (计划实现)
/// ✅ 上下分屏显示 (计划实现)
/// ✅ 分屏比例调节 (计划实现)
/// ✅ 响应式布局适配 (计划实现)
```

**核心功能**:
1. **分屏布局**: 支持左右/上下分屏模式
2. **比例调节**: 拖拽分隔线调整视图比例
3. **响应式**: 自动适配不同设备屏幕
4. **手势支持**: 丰富的手势交互

**实现重点**:
```dart
class SplitViewWidget extends StatefulWidget {
  final Widget leftChild;
  final Widget rightChild;
  final double initialRatio;
  final Axis direction;
  
  @override
  _SplitViewWidgetState createState() => _SplitViewWidgetState();
}

class _SplitViewWidgetState extends State<SplitViewWidget> {
  double _ratio = 0.5;
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Flex(
          direction: widget.direction,
          children: [
            Flexible(
              flex: (_ratio * 100).round(),
              child: widget.leftChild,
            ),
            GestureDetector(
              onPanUpdate: _handleDividerDrag,
              child: _buildDivider(),
            ),
            Flexible(
              flex: ((1 - _ratio) * 100).round(),
              child: widget.rightChild,
            ),
          ],
        );
      },
    );
  }
}
```

### **优先级2: 同步滚动实现 (3天)**

#### **模块**: `flutter_app/lib/widgets/sync_scroll_widget.dart`
```dart
/// 🧩 同步滚动组件 - 最小模块化设计
/// 
/// 功能实现:
/// ✅ 双向滚动同步 (计划实现)
/// ✅ 平滑滚动动画 (计划实现)
/// ✅ 位置精确映射 (计划实现)
/// ✅ 滚动状态管理 (计划实现)
```

**核心算法**:
1. **位置映射**: 原文位置与重排文本位置的精确映射
2. **同步滚动**: 一侧滚动时另一侧自动同步
3. **平滑动画**: 提供流畅的滚动体验
4. **状态管理**: 管理滚动状态和同步状态

### **优先级3: 实时编辑功能 (4天)**

#### **模块**: `flutter_app/lib/widgets/editable_text_widget.dart`
```dart
/// 🧩 可编辑文本组件 - 最小模块化设计
/// 
/// 功能实现:
/// ✅ 实时文本编辑 (计划实现)
/// ✅ 变更检测 (计划实现)
/// ✅ 撤销重做支持 (计划实现)
/// ✅ 语法高亮 (计划实现)
```

**核心功能**:
1. **实时编辑**: 支持直接在重排文本上编辑
2. **变更追踪**: 实时追踪文本变更并同步到后端
3. **撤销重做**: 支持编辑操作的撤销和重做
4. **格式保持**: 保持文本的基本格式和样式

---

## 🎵 **Week 3-4: 辅助界面开发**

### **优先级1: TTS浮窗控制 (3天)**

#### **模块**: `flutter_app/lib/widgets/tts_floating_panel.dart`
```dart
/// 🧩 TTS浮窗面板 - 最小模块化设计
/// 
/// 功能实现:
/// ✅ 浮窗显示和隐藏 (计划实现)
/// ✅ 拖拽和自动吸附 (计划实现)
/// ✅ 播放控制界面 (计划实现)
/// ✅ 透明度动画 (计划实现)
```

**核心功能**:
1. **浮窗管理**: 浮窗的显示、隐藏、位置控制
2. **拖拽吸附**: 支持拖拽并自动吸附到屏幕边缘
3. **播放控制**: 播放/暂停/停止/速度调节
4. **透明度**: 自动透明度调节和动画效果

**实现重点**:
```dart
class TTSFloatingPanel extends StatefulWidget {
  @override
  _TTSFloatingPanelState createState() => _TTSFloatingPanelState();
}

class _TTSFloatingPanelState extends State<TTSFloatingPanel>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;
  Offset _position = const Offset(300, 100);
  bool _isExpanded = false;
  
  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onPanUpdate: _handlePanUpdate,
        onPanEnd: _handlePanEnd,
        onTap: _toggleExpanded,
        child: AnimatedBuilder(
          animation: _opacityAnimation,
          builder: (context, child) {
            return Opacity(
              opacity: _opacityAnimation.value,
              child: _buildPanel(),
            );
          },
        ),
      ),
    );
  }
}
```

### **优先级2: 设置管理界面 (4天)**

#### **模块**: `flutter_app/lib/screens/settings_screen.dart`
```dart
/// 🧩 设置界面 - 最小模块化设计
/// 
/// 功能实现:
/// ✅ 阅读参数设置 (计划实现)
/// ✅ TTS参数配置 (计划实现)
/// ✅ 系统设置管理 (计划实现)
/// ✅ 设置数据持久化 (计划实现)
```

**设置分类**:
1. **阅读设置**: 字体、字号、行距、页边距、主题
2. **TTS设置**: 语音参数、音色选择、播放速度
3. **显示设置**: 亮度、色温、夜间模式
4. **系统设置**: 缓存管理、数据备份、隐私设置

### **优先级3: 书库管理界面 (3天)**

#### **模块**: `flutter_app/lib/screens/library_screen.dart`
```dart
/// 🧩 书库界面 - 最小模块化设计
/// 
/// 功能实现:
/// ✅ 书籍列表显示 (计划实现)
/// ✅ 分类和搜索 (计划实现)
/// ✅ 导入和管理 (计划实现)
/// ✅ 最近阅读 (计划实现)
```

**核心功能**:
1. **书籍列表**: 网格/列表视图显示书籍
2. **分类管理**: 按作者、类型、标签分类
3. **搜索功能**: 书名、作者、内容搜索
4. **导入管理**: 文件导入、删除、移动

---

## 🔗 **FFI接口集成**

### **Rust FFI接口**:
```rust
// 主要的FFI接口函数
#[no_mangle]
pub extern "C" fn load_document(path: *const c_char) -> i64;

#[no_mangle]
pub extern "C" fn get_page_content(doc_id: i64, page: i32) -> *const c_char;

#[no_mangle]
pub extern "C" fn get_reflow_content(doc_id: i64, page: i32) -> *const c_char;

#[no_mangle]
pub extern "C" fn sync_scroll_position(original_pos: f64) -> f64;

#[no_mangle]
pub extern "C" fn update_text_content(doc_id: i64, page: i32, text: *const c_char) -> i32;

#[no_mangle]
pub extern "C" fn start_tts_playback(text: *const c_char, voice_id: i32) -> i32;
```

### **Flutter调用示例**:
```dart
class RustBridge {
  static const MethodChannel _channel = MethodChannel('rust_bridge');
  
  static Future<int> loadDocument(String path) async {
    return await _channel.invokeMethod('load_document', {'path': path});
  }
  
  static Future<String> getPageContent(int docId, int page) async {
    return await _channel.invokeMethod('get_page_content', {
      'doc_id': docId,
      'page': page,
    });
  }
  
  static Future<double> syncScrollPosition(double originalPos) async {
    return await _channel.invokeMethod('sync_scroll_position', {
      'original_pos': originalPos,
    });
  }
}
```

---

## 📊 **第二阶段交付成果**

### **核心界面**:
1. ✅ **主阅读界面**: 高质量PDF阅读体验
2. ✅ **对照编辑界面**: 创新的分屏对照编辑功能
3. ✅ **TTS浮窗**: 优雅的语音控制界面
4. ✅ **设置界面**: 完整的参数配置系统
5. ✅ **书库界面**: 便捷的书籍管理系统

### **用户体验**:
- 📱 **流畅性**: 60fps的流畅动画和交互
- 🎯 **精确性**: 精确的滚动同步和位置映射
- ✏️ **便捷性**: 直观的编辑和标注功能
- 🎵 **智能性**: 智能的TTS控制和语音合成
- ⚙️ **个性化**: 丰富的个性化设置选项

### **技术指标**:
- 📱 **启动速度**: < 3秒
- 🔄 **页面切换**: < 500ms
- ✏️ **编辑响应**: < 100ms
- 🎵 **TTS延迟**: < 500ms
- 💾 **内存使用**: < 300MB

---

## 🧪 **测试和验证**

### **功能测试**:
1. **界面功能**: 所有界面功能完整测试
2. **交互测试**: 手势和触摸交互测试
3. **性能测试**: 流畅度和响应速度测试
4. **兼容性测试**: 不同设备和屏幕尺寸测试

### **用户体验测试**:
1. **可用性测试**: 真实用户使用场景测试
2. **易用性测试**: 功能学习成本评估
3. **满意度测试**: 用户满意度调研
4. **反馈收集**: 用户反馈收集和改进

---

## 🎯 **成功标准**

### **功能完整性**:
- ✅ 所有界面功能100%实现
- ✅ 前后端集成100%完成
- ✅ 用户交互100%流畅
- ✅ 数据同步100%准确

### **用户体验**:
- ✅ 界面美观度 > 85%
- ✅ 操作便捷性 > 90%
- ✅ 功能易用性 > 90%
- ✅ 整体满意度 > 88%

### **技术质量**:
- ✅ 代码质量100%符合标准
- ✅ 性能指标100%达标
- ✅ 兼容性100%满足
- ✅ 稳定性100%可靠

**第二阶段完成后，将拥有完整的Flutter前端界面，提供卓越的用户体验！**
