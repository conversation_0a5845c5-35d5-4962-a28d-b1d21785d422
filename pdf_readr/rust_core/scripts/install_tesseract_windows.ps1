# Tesseract Windows安装脚本
# 
# 功能实现:
# ✅ 自动下载Tesseract安装包 (在第10至30行完整实现)
# ✅ 静默安装Tesseract (在第35至55行完整实现)
# ✅ 配置环境变量 (在第60至80行完整实现)
# ✅ 下载中文语言包 (在第85至105行完整实现)
# ✅ 验证安装结果 (在第110至130行完整实现)
#
# 法律声明:
# 此脚本为Augment Agent原创设计，用于自动安装Tesseract OCR。
# 安装的是开源软件Tesseract，遵循Apache 2.0许可证。

Write-Host "🔧 Tesseract OCR Windows安装程序" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 设置下载目录
$downloadDir = "$env:TEMP\tesseract_install"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir -Force | Out-Null
}

Write-Host "📦 开始下载Tesseract安装包..." -ForegroundColor Cyan

# Tesseract下载URL (官方GitHub releases)
$tesseractUrl = "https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3/tesseract-ocr-w64-setup-5.3.3.20231005.exe"
$installerPath = "$downloadDir\tesseract-installer.exe"

try {
    # 下载Tesseract安装包
    Write-Host "  📥 正在下载: $tesseractUrl" -ForegroundColor White
    Invoke-WebRequest -Uri $tesseractUrl -OutFile $installerPath -UseBasicParsing
    Write-Host "  ✅ 下载完成: $installerPath" -ForegroundColor Green
} catch {
    Write-Host "  ❌ 下载失败: $_" -ForegroundColor Red
    Write-Host "  💡 请检查网络连接或手动下载安装包" -ForegroundColor Yellow
    Write-Host "  🔗 手动下载地址: https://github.com/UB-Mannheim/tesseract/releases" -ForegroundColor Yellow
    pause
    exit 1
}

Write-Host "🚀 开始安装Tesseract..." -ForegroundColor Cyan

# 静默安装Tesseract
$installPath = "C:\Program Files\Tesseract-OCR"
try {
    Write-Host "  📦 正在安装到: $installPath" -ForegroundColor White
    $installArgs = @(
        "/S",  # 静默安装
        "/D=$installPath"  # 安装目录
    )
    
    Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait -NoNewWindow
    Write-Host "  ✅ Tesseract安装完成" -ForegroundColor Green
} catch {
    Write-Host "  ❌ 安装失败: $_" -ForegroundColor Red
    pause
    exit 1
}

Write-Host "🔧 配置环境变量..." -ForegroundColor Cyan

# 添加到系统PATH
$tesseractBinPath = "$installPath"
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")

if ($currentPath -notlike "*$tesseractBinPath*") {
    try {
        Write-Host "  📝 添加到系统PATH: $tesseractBinPath" -ForegroundColor White
        $newPath = "$currentPath;$tesseractBinPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
        
        # 更新当前会话的PATH
        $env:PATH = "$env:PATH;$tesseractBinPath"
        Write-Host "  ✅ 环境变量配置完成" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ 环境变量配置失败: $_" -ForegroundColor Red
        Write-Host "  💡 请手动添加到PATH: $tesseractBinPath" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ✅ PATH中已存在Tesseract路径" -ForegroundColor Green
}

Write-Host "🌍 下载中文语言包..." -ForegroundColor Cyan

# 下载中文语言包
$tessdataDir = "$installPath\tessdata"
$languagePackages = @(
    @{Name="chi_sim"; Url="https://github.com/tesseract-ocr/tessdata/raw/main/chi_sim.traineddata"; File="chi_sim.traineddata"},
    @{Name="chi_tra"; Url="https://github.com/tesseract-ocr/tessdata/raw/main/chi_tra.traineddata"; File="chi_tra.traineddata"},
    @{Name="eng"; Url="https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata"; File="eng.traineddata"}
)

foreach ($package in $languagePackages) {
    $langFile = "$tessdataDir\$($package.File)"
    if (!(Test-Path $langFile)) {
        try {
            Write-Host "  📥 下载$($package.Name)语言包..." -ForegroundColor White
            Invoke-WebRequest -Uri $package.Url -OutFile $langFile -UseBasicParsing
            Write-Host "  ✅ $($package.Name)语言包下载完成" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠️  $($package.Name)语言包下载失败: $_" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ✅ $($package.Name)语言包已存在" -ForegroundColor Green
    }
}

Write-Host "🧪 验证安装..." -ForegroundColor Cyan

# 验证Tesseract安装
try {
    Write-Host "  🔍 检查Tesseract版本..." -ForegroundColor White
    $version = & tesseract --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Tesseract安装成功!" -ForegroundColor Green
        Write-Host "  📋 版本信息:" -ForegroundColor White
        $version | ForEach-Object { Write-Host "    $_" -ForegroundColor Gray }
    } else {
        Write-Host "  ❌ Tesseract命令执行失败" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Tesseract验证失败: $_" -ForegroundColor Red
    Write-Host "  💡 请重启命令行或重新登录系统" -ForegroundColor Yellow
}

# 验证语言包
try {
    Write-Host "  🔍 检查可用语言..." -ForegroundColor White
    $languages = & tesseract --list-langs 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ 可用语言列表:" -ForegroundColor Green
        $languages | ForEach-Object { 
            if ($_ -match "^(eng|chi_sim|chi_tra)$") {
                Write-Host "    ✅ $_" -ForegroundColor Green
            } elseif ($_ -notmatch "^List of available languages") {
                Write-Host "    📋 $_" -ForegroundColor Gray
            }
        }
    }
} catch {
    Write-Host "  ⚠️  语言列表检查失败" -ForegroundColor Yellow
}

# 清理下载文件
Write-Host "🧹 清理临时文件..." -ForegroundColor Cyan
try {
    Remove-Item -Path $downloadDir -Recurse -Force
    Write-Host "  ✅ 临时文件清理完成" -ForegroundColor Green
} catch {
    Write-Host "  ⚠️  临时文件清理失败" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Tesseract安装完成!" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host "📍 安装路径: $installPath" -ForegroundColor White
Write-Host "🌍 支持语言: 英文(eng), 中文简体(chi_sim), 中文繁体(chi_tra)" -ForegroundColor White
Write-Host ""
Write-Host "🔄 下一步操作:" -ForegroundColor Cyan
Write-Host "  1. 重启命令行或IDE" -ForegroundColor White
Write-Host "  2. 运行OCR集成测试: cargo run --bin test_ocr_integration" -ForegroundColor White
Write-Host "  3. 如果仍有问题，请重新登录系统" -ForegroundColor White
Write-Host ""

pause
