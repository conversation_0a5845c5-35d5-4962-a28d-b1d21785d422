
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告 - test_1753194000_a9cbffe4</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background-color: white; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            overflow: hidden; 
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .content { padding: 30px; }
        .section { margin-bottom: 30px; }
        .section h2 { 
            color: #333; 
            border-bottom: 2px solid #667eea; 
            padding-bottom: 10px; 
            margin-bottom: 20px; 
        }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin-bottom: 20px; 
        }
        .stat-card { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            text-align: center; 
            border-left: 4px solid #667eea; 
        }
        .stat-value { 
            font-size: 2em; 
            font-weight: bold; 
            color: #667eea; 
            margin-bottom: 5px; 
        }
        .stat-label { color: #666; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        .recommendations { 
            background: #e3f2fd; 
            padding: 20px; 
            border-radius: 8px; 
            border-left: 4px solid #2196f3; 
        }
        .recommendations ul { margin: 0; padding-left: 20px; }
        .env-info { 
            background: #f1f8e9; 
            padding: 20px; 
            border-radius: 8px; 
            border-left: 4px solid #8bc34a; 
        }
        .env-info dl { margin: 0; }
        .env-info dt { font-weight: bold; color: #333; margin-top: 10px; }
        .env-info dd { margin: 5px 0 0 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 测试执行报告</h1>
            <p>执行ID: test_1753194000_a9cbffe4 | 时间: SystemTime { intervals: 133976676003957549 }</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📈 测试摘要</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">10</div>
                        <div class="stat-label">总测试数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value success">10</div>
                        <div class="stat-label">通过测试</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value danger">0</div>
                        <div class="stat-label">失败测试</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">100.00%</div>
                        <div class="stat-label">成功率</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>⚡ 性能分析</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value success">8.9/10</div>
                        <div class="stat-label">总体分数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value warning">2</div>
                        <div class="stat-label">性能瓶颈</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">1</div>
                        <div class="stat-label">改进建议</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value success">否</div>
                        <div class="stat-label">性能回归</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🖥️ 环境信息</h2>
                <div class="env-info">
                    <dl>
                        <dt>Rust版本</dt>
                        <dd></dd>
                        <dt>目标平台</dt>
                        <dd>x86_64</dd>
                        <dt>CPU信息</dt>
                        <dd>Unknown CPU</dd>
                        <dt>总内存</dt>
                        <dd>8192.0 MB</dd>
                        <dt>构建类型</dt>
                        <dd>Debug</dd>
                        <dt>优化级别</dt>
                        <dd>0</dd>
                    </dl>
                </div>
            </div>

            <div class="section">
                <h2>💡 优化建议</h2>
                <div class="recommendations">
                    <ul>
                        <li>优化操作吞吐量</li><li>优化缓存效率</li><li>缓存优化优化</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        