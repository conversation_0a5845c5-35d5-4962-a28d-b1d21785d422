[package]
name = "pdf_reader_core"

# 暂时排除有问题的测试文件和二进制文件
exclude = [
    "tests/comprehensive_test_suite.rs",
    "tests/storage_integration_tests.rs",
    "tests/integration_tests.rs",
    "src/bin/performance_benchmark.rs"
]
version = "0.1.0"
edition = "2021"
description = "智能PDF阅读器核心库 - 立即可用版本"
license = "MIT"
authors = ["Augment Agent <<EMAIL>>"]

[lib]
name = "pdf_reader_core"
crate-type = ["rlib"]

[dependencies]
# Tesseract OCR 集成 - 简化版本 (避免vcpkg依赖)
# tesseract = { version = "0.13.0", optional = true }
# leptess = { version = "0.14.0", optional = true }
# leptonica-plumbing = { version = "1.0", optional = true }
# 使用简化的Tesseract绑定 (无需vcpkg)
rusty-tesseract = { version = "1.1.10", optional = true }

# PaddleOCR 集成 (暂时注释，因为包不存在于 crates.io)
# paddle-ocr = { version = "0.1.0", optional = true }
# 或者使用 git 版本（如果 crates.io 版本不可用）
# paddle-ocr = { git = "https://github.com/paddle-ocr-rs/paddle-ocr", optional = true }
# 基础依赖 - 优化版本
serde = { version = "1.0.219", features = ["derive"] }
serde_json = { version = "1.0.140", features = ["std"] }

# PP-OCRv4 集成依赖
async-trait = { version = "0.1.74", optional = true }

# 数据库依赖 - 优化配置
sqlx = { version = "0.7.4", features = ["runtime-tokio-rustls", "sqlite", "chrono"], optional = true }
tokio = { version = "1.40.0", features = ["rt", "macros", "time", "rt-multi-thread", "sync"], optional = true }
chrono = { version = "0.4.38", features = ["serde"], optional = true }
once_cell = { version = "1.21.3", optional = true }
num_cpus = { version = "1.0", optional = true }
rmp-serde = { version = "1.1", optional = true }

# PDF处理依赖
lopdf = { version = "0.26.0", optional = true }

# OCR处理依赖 - 使用稳定版本
image = { version = "0.24.7", default-features = false, features = ["png", "jpeg"], optional = true }
# 使用更稳定的OCR库替代tesseract
rten = { version = "0.10.0", optional = true }  # 纯Rust OCR引擎
imageproc = { version = "0.23.0", optional = true }  # 图像预处理

# TTS处理依赖
tts = { version = "0.26.0", optional = true }

# 导出功能依赖
zip = { version = "0.6.6", optional = true }  # EPUB/DOCX文件打包

# FFI桥接依赖（serde_json已在基础依赖中定义）
libc = { version = "0.2", optional = true }

# 词典系统依赖
rusqlite = { version = "0.30.0", features = ["bundled"], optional = true }  # SQLite数据库

# 搜索系统依赖（使用现有的regex依赖）
sha2 = "0.10"

# 必需的基础依赖
regex = { version = "1.11.1", default-features = false, features = ["std"], optional = true }




# 基础错误处理 - 重新启用
anyhow = { version = "1.0", optional = true }
thiserror = { version = "1.0", optional = true }

# 优化的日志记录
log = { version = "0.4.27", default-features = false, optional = true }
env_logger = { version = "0.10.0", optional = true }

# UUID生成 - 优化版本
uuid = { version = "1.17.0", default-features = false, features = ["v4", "fast-rng"], optional = true }

# 文本处理 - 优化版本 (已在基础依赖中定义)



# 随机数生成 - 优化版本
rand = { version = "0.8.5", default-features = false, features = ["std", "std_rng"], optional = true }

# 文件处理 - 优化版本
walkdir = { version = "2.5.0", optional = true }
tempfile = { version = "3.20.0", optional = true }
dirs = { version = "5.0.1", optional = true }

# 加密 - 优化版本
base64 = { version = "0.22.1", default-features = false, features = ["std"], optional = true }
hex = { version = "0.4.3", default-features = false, features = ["std"], optional = true }

# 配置文件处理 - 优化版本
toml = { version = "0.8.19", default-features = false, features = ["parse"], optional = true }

# OCR引擎集成 - Tesseract (已在上面定义，这里删除重复项)

# 图像处理 - OCR支持 (使用已有的image和imageproc依赖)

# 语言检测 - OCR增强
whatlang = { version = "0.16", optional = true }

# 拼写检查 - OCR后处理
spellcheck = { version = "0.1", optional = true }

# TTS语音合成引擎 (使用已有的tts依赖)

# 音频处理
rodio = { version = "0.17", optional = true }
hound = { version = "3.5", optional = true }

# 语音克隆和AI模型 - 暂时禁用，存在依赖冲突
# candle-core = { version = "0.4.1", optional = true }
# candle-nn = { version = "0.4.1", optional = true }
# candle-transformers = { version = "0.4.1", optional = true }



# Windows特定依赖
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "processthreadsapi"], optional = true }

[features]
# 默认特性 - 包含PDF和OCR开发所需功能
default = ["basic", "database", "performance", "pdf", "render", "export", "dictionary", "search"]

# 基础特性 - 核心序列化和日志功能
basic = ["log", "dep:uuid", "dep:chrono", "dep:anyhow", "dep:thiserror", "dep:async-trait", "dep:env_logger"]

# 核心功能特性
database = ["basic", "dep:sqlx", "dep:tokio", "dep:chrono", "dep:once_cell"]
pdf = ["basic", "dep:lopdf"]
ocr = ["basic", "dep:rusty-tesseract", "dep:image", "dep:imageproc", "dep:whatlang", "dep:spellcheck", "dep:dirs", "dep:tokio", "dep:base64", "dep:async-trait"]
# OCR 引擎特性
tesseract = ["dep:rusty-tesseract"]
paddle-ocr = ["dep:base64", "dep:async-trait", "dep:tokio"]  # PP-OCRv4 移动端支持
full-ocr = ["tesseract", "paddle-ocr", "ocr"]
ffi = ["basic", "dep:libc", "dep:tokio", "dep:rmp-serde"]
render = ["basic", "dep:image"]
tts = ["basic", "dep:tts", "dep:rodio", "dep:hound", "dep:tokio"]
# voice_cloning = ["tts", "dep:candle-core", "dep:candle-nn", "dep:candle-transformers"]  # 暂时禁用，存在依赖冲突
dictionary = ["basic", "dep:rusqlite", "dep:uuid", "dep:tokio"]
search = ["basic", "dep:rusqlite", "dep:regex", "dep:uuid", "dep:tokio"]
toc = ["basic", "dep:uuid", "dep:tokio"]
cache = ["basic"]
api = ["basic"]

# 高级功能特性
async_tasks = ["basic", "dep:tokio"]
version_control = ["basic"]
reflow = ["basic"]
preload = ["basic", "dep:tokio"]
export = ["basic", "dep:zip", "dep:uuid", "dep:chrono"]

# 工具特性
files = ["basic", "dep:walkdir", "dep:tempfile", "dep:dirs"]
crypto = ["basic", "dep:base64", "dep:hex"]
config = ["basic", "dep:toml"]
text = ["basic", "dep:regex"]
time = ["basic", "dep:chrono"]
random = ["basic", "dep:rand"]

# 性能特性
performance = ["basic", "dep:tokio", "dep:num_cpus", "random"]

# 平台特性
windows = ["dep:winapi"]

# 组合特性
core = ["basic", "database", "pdf", "ffi"]
advanced = ["core", "ocr", "render", "tts", "dictionary", "search", "toc", "cache", "api", "performance"]
full = ["advanced", "async_tasks", "version_control", "reflow", "preload", "export", "files", "crypto", "config", "text", "time", "random"]

# 移动端优化特性
mobile = ["core", "cache", "preload", "performance"]

# 开发特性
dev = ["full", "windows"]

# 优化的性能配置
[profile.release]
opt-level = 3              # 最高优化级别
lto = "thin"               # 启用瘦链接时优化，平衡编译时间和性能
codegen-units = 1          # 减少代码生成单元，提高优化效果
panic = "abort"            # 发布版本使用abort，减少二进制大小
strip = true               # 移除调试符号，减少二进制大小
overflow-checks = false    # 发布版本禁用溢出检查，提高性能

[profile.dev]
opt-level = 0              # 开发版本不优化，加快编译
debug = true               # 保留调试信息
overflow-checks = true     # 开发版本启用溢出检查

# 优化的开发配置
[profile.dev.package."*"]
opt-level = 2              # 依赖库使用O2优化，提高开发体验

# 性能和安全相关依赖（暂时禁用以避免编译问题）
# parking_lot = { version = "0.12.3", optional = true }  # 高性能锁
# dashmap = { version = "6.1.0", optional = true }       # 高性能并发HashMap
# rayon = { version = "1.10.0", optional = true }        # 数据并行处理

# 特性配置已在上面定义，删除重复的 [features] 表

[dev-dependencies]
tempfile = "3.20.0"
criterion = { version = "0.5.1", features = ["html_reports"] }  # 性能基准测试
tokio = { version = "1.0", features = ["full"] }  # 异步运行时，用于测试
chrono = { version = "0.4", features = ["serde"] }  # 时间处理库，用于测试
