
from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image(text, filename, font_size=40, image_size=(800, 200)):
    # 创建白色背景图像
    image = Image.new('RGB', image_size, 'white')
    draw = ImageDraw.Draw(image)
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # 使用默认字体
        font = ImageFont.load_default()
    
    # 计算文本位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (image_size[0] - text_width) // 2
    y = (image_size[1] - text_height) // 2
    
    # 绘制文本
    draw.text((x, y), text, fill='black', font=font)
    
    # 保存图像
    image.save(filename)
    print(f"测试图像已创建: {filename}")

# 创建各种测试图像
test_texts = [
    ("Hello World! This is a test.", "test_english.png"),
    ("你好世界！这是一个测试。", "test_chinese.png"),
    ("こんにちは世界！これはテストです。", "test_japanese.png"),
    ("안녕하세요 세계! 이것은 테스트입니다.", "test_korean.png"),
    ("Mixed 混合 Text テキスト 텍스트", "test_mixed.png"),
    ("1234567890 !@#$%^&*()", "test_numbers.png"),
]

for text, filename in test_texts:
    create_test_image(text, filename)

print("所有测试图像创建完成")
