
import time
import json
import os
import sys
from pathlib import Path

# 添加模型路径
sys.path.append('../tesseract')
sys.path.append('../easyocr')
sys.path.append('../trocr')

def test_tesseract():
    try:
        import pytesseract
        from PIL import Image
        
        results = []
        test_images = ['test_english.png', 'test_chinese.png', 'test_mixed.png']
        
        for img_name in test_images:
            if os.path.exists(img_name):
                start_time = time.time()
                image = Image.open(img_name)
                text = pytesseract.image_to_string(image, lang='eng+chi_sim')
                end_time = time.time()
                
                results.append({
                    'image': img_name,
                    'text': text.strip(),
                    'processing_time': end_time - start_time,
                    'engine': 'Tesseract 5.5.1'
                })
        
        return results
    except Exception as e:
        print(f"Tesseract测试失败: {e}")
        return []

def test_easyocr():
    try:
        import easyocr
        
        reader = easyocr.Reader(['en', 'ch_sim'], gpu=False)
        results = []
        test_images = ['test_english.png', 'test_chinese.png', 'test_mixed.png']
        
        for img_name in test_images:
            if os.path.exists(img_name):
                start_time = time.time()
                result = reader.readtext(img_name, detail=0)
                text = ' '.join(result)
                end_time = time.time()
                
                results.append({
                    'image': img_name,
                    'text': text,
                    'processing_time': end_time - start_time,
                    'engine': 'EasyOCR 1.7.2'
                })
        
        return results
    except Exception as e:
        print(f"EasyOCR测试失败: {e}")
        return []

def test_trocr():
    try:
        from transformers import TrOCRProcessor, VisionEncoderDecoderModel
        from PIL import Image
        
        processor = TrOCRProcessor.from_pretrained("microsoft/trocr-base-printed")
        model = VisionEncoderDecoderModel.from_pretrained("microsoft/trocr-base-printed")
        
        results = []
        test_images = ['test_english.png', 'test_chinese.png', 'test_mixed.png']
        
        for img_name in test_images:
            if os.path.exists(img_name):
                start_time = time.time()
                image = Image.open(img_name).convert('RGB')
                pixel_values = processor(images=image, return_tensors="pt").pixel_values
                generated_ids = model.generate(pixel_values)
                text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
                end_time = time.time()
                
                results.append({
                    'image': img_name,
                    'text': text,
                    'processing_time': end_time - start_time,
                    'engine': 'TrOCR Base'
                })
        
        return results
    except Exception as e:
        print(f"TrOCR测试失败: {e}")
        return []

# 运行所有测试
print("开始性能测试...")

all_results = []
all_results.extend(test_tesseract())
all_results.extend(test_easyocr())
all_results.extend(test_trocr())

# 保存结果
with open('performance_results.json', 'w', encoding='utf-8') as f:
    json.dump(all_results, f, ensure_ascii=False, indent=2)

print("性能测试完成，结果已保存到 performance_results.json")

# 打印结果摘要
print("\n=== 测试结果摘要 ===")
for result in all_results:
    print(f"引擎: {result['engine']}")
    print(f"图像: {result['image']}")
    print(f"识别文本: {result['text'][:50]}...")
    print(f"处理时间: {result['processing_time']:.3f}秒")
    print("-" * 50)
