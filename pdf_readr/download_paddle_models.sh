#!/bin/bash

# PaddleOCR 模型自动下载脚本

echo "========================================"
echo "    PaddleOCR 模型自动下载脚本"
echo "========================================"
echo

# 设置模型目录
MODEL_DIR="./models/paddle_ocr"
TEMP_DIR="./temp_downloads"

# 创建目录
echo "📁 创建模型目录..."
mkdir -p "$MODEL_DIR"/{det,rec,cls}
mkdir -p "$TEMP_DIR"

# 模型下载 URL
DET_MODEL_URL="https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_det_infer.tar"
REC_MODEL_URL="https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_rec_infer.tar"
CLS_MODEL_URL="https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar"
DICT_URL="https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.6/ppocr/utils/ppocr_keys_v1.txt"

# 检查下载工具
if command -v wget &> /dev/null; then
    DOWNLOAD_CMD="wget -O"
    echo "✅ 使用 wget 下载"
elif command -v curl &> /dev/null; then
    DOWNLOAD_CMD="curl -L -o"
    echo "✅ 使用 curl 下载"
else
    echo "❌ 未找到 wget 或 curl，请安装其中一个"
    exit 1
fi

# 下载函数
download_and_extract() {
    local name=$1
    local url=$2
    local target_dir=$3
    local filename=$(basename "$url")
    
    echo
    echo "📦 下载 $name..."
    echo "🔗 URL: $url"
    
    # 下载文件
    if $DOWNLOAD_CMD "$TEMP_DIR/$filename" "$url"; then
        echo "✅ $name 下载成功"
        
        # 解压文件
        echo "📂 解压 $name..."
        if tar -xf "$TEMP_DIR/$filename" -C "$target_dir" --strip-components=1; then
            echo "✅ $name 解压成功"
            
            # 清理下载文件
            rm -f "$TEMP_DIR/$filename"
        else
            echo "❌ $name 解压失败"
            return 1
        fi
    else
        echo "❌ $name 下载失败"
        return 1
    fi
}

# 下载字典文件
download_dict() {
    echo
    echo "📚 下载字符字典..."
    
    if $DOWNLOAD_CMD "$MODEL_DIR/ppocr_keys_v1.txt" "$DICT_URL"; then
        echo "✅ 字符字典下载成功"
    else
        echo "❌ 字符字典下载失败"
        return 1
    fi
}

# 验证模型文件
verify_models() {
    echo
    echo "🔍 验证模型文件..."
    
    local all_good=true
    
    # 检查检测模型
    if [[ -f "$MODEL_DIR/det/inference.pdmodel" && -f "$MODEL_DIR/det/inference.pdiparams" ]]; then
        echo "✅ 检测模型文件完整"
    else
        echo "❌ 检测模型文件缺失"
        all_good=false
    fi
    
    # 检查识别模型
    if [[ -f "$MODEL_DIR/rec/inference.pdmodel" && -f "$MODEL_DIR/rec/inference.pdiparams" ]]; then
        echo "✅ 识别模型文件完整"
    else
        echo "❌ 识别模型文件缺失"
        all_good=false
    fi
    
    # 检查分类模型
    if [[ -f "$MODEL_DIR/cls/inference.pdmodel" && -f "$MODEL_DIR/cls/inference.pdiparams" ]]; then
        echo "✅ 分类模型文件完整"
    else
        echo "❌ 分类模型文件缺失"
        all_good=false
    fi
    
    # 检查字典文件
    if [[ -f "$MODEL_DIR/ppocr_keys_v1.txt" ]]; then
        echo "✅ 字符字典文件存在"
    else
        echo "❌ 字符字典文件缺失"
        all_good=false
    fi
    
    if $all_good; then
        echo "🎉 所有模型文件验证通过！"
        return 0
    else
        echo "⚠️  部分模型文件缺失或损坏"
        return 1
    fi
}

# 显示模型信息
show_model_info() {
    echo
    echo "📊 模型文件信息:"
    echo "----------------------------------------"
    
    if [[ -d "$MODEL_DIR" ]]; then
        echo "📁 模型目录: $MODEL_DIR"
        echo "📏 总大小: $(du -sh "$MODEL_DIR" 2>/dev/null | cut -f1 || echo "未知")"
        echo
        
        for subdir in det rec cls; do
            if [[ -d "$MODEL_DIR/$subdir" ]]; then
                echo "📂 $subdir 模型:"
                ls -la "$MODEL_DIR/$subdir/" | grep -E "\.(pdmodel|pdiparams)" | while read -r line; do
                    echo "   $line"
                done
                echo
            fi
        done
        
        if [[ -f "$MODEL_DIR/ppocr_keys_v1.txt" ]]; then
            echo "📚 字符字典:"
            echo "   $(ls -la "$MODEL_DIR/ppocr_keys_v1.txt")"
            echo "   字符数量: $(wc -l < "$MODEL_DIR/ppocr_keys_v1.txt" 2>/dev/null || echo "未知")"
        fi
    fi
}

# 清理函数
cleanup() {
    echo
    echo "🧹 清理临时文件..."
    rm -rf "$TEMP_DIR"
    echo "✅ 清理完成"
}

# 主下载流程
main() {
    echo "🚀 开始下载 PaddleOCR 模型..."
    
    # 检查是否已存在模型文件
    if verify_models &>/dev/null; then
        echo "✅ 模型文件已存在且完整"
        read -p "是否重新下载？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "跳过下载"
            show_model_info
            exit 0
        fi
        echo "🔄 重新下载模型文件..."
        rm -rf "$MODEL_DIR"/{det,rec,cls}/*
    fi
    
    # 下载各个模型
    download_and_extract "检测模型" "$DET_MODEL_URL" "$MODEL_DIR/det" || exit 1
    download_and_extract "识别模型" "$REC_MODEL_URL" "$MODEL_DIR/rec" || exit 1
    download_and_extract "分类模型" "$CLS_MODEL_URL" "$MODEL_DIR/cls" || exit 1
    download_dict || exit 1
    
    # 验证下载结果
    if verify_models; then
        echo
        echo "========================================"
        echo "        下载完成！"
        echo "========================================"
        echo
        echo "✅ 所有 PaddleOCR 模型下载成功"
        echo
        echo "📋 下一步："
        echo "1. 编译项目: cargo build --features paddle-ocr"
        echo "2. 运行测试: cargo test --features paddle-ocr"
        echo "3. 开始使用 PaddleOCR 进行文本识别"
        echo
        
        show_model_info
    else
        echo
        echo "❌ 模型下载或验证失败"
        echo "💡 请检查网络连接并重试"
        exit 1
    fi
}

# 设置清理陷阱
trap cleanup EXIT

# 运行主函数
main

echo
echo "🎉 PaddleOCR 模型安装完成！"
